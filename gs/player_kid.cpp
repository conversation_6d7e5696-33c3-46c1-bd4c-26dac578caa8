#include <cstring>
#include <spinlock.h>
#include <vector.h>
#include "config.h"
#include "player_kid.h"
#include "world.h"
#include "worldmanager.h"
#include "player_imp.h"

bool player_kid::CreateKid(const void *buf)
{
    printf("\nPlayer_kid.cpp class -> CreateKid Init");
    signed int count; // [esp+Ch] [ebp-1Ch]

    spin_autolock l(this->_lock_data_map);

    if (this->_kid_data._status)
    {
        return 1;
    }
    else
    {
        memset(&this->_kid_data, 0, sizeof(this->_kid_data));
        count = *((char *)buf + 1);
        if (count > 16)
            count = 16;
        memcpy(this->_kid_data._name, (char *)buf + 2, count);
        this->_kid_data._pool_lvl = 1;
        this->_kid_data._gender = *(unsigned char *)buf != 0;
        this->_kid_data._status = 1;
        this->_kid_data._free_times = 1;
        RePool();
        this->_kid_data._free_times = 1;
        _owner->_runner->send_kid_val9(buf);
        ClientSync(1);
        return 1;
    }
}

bool player_kid::RePool()
{
    printf("\nPlayer_kid.cpp class -> Repool Init");
    int i_0;       // [esp+Ch] [ebp-4Ch]
    int i_0a;      // [esp+Ch] [ebp-4Ch]
    int tmp_0;     // [esp+10h] [ebp-48h]
    int tmp;       // [esp+18h] [ebp-40h]
    int i;         // [esp+1Ch] [ebp-3Ch]
    int tmp_p[10]; // [esp+20h] [ebp-38h]

    if (!this->_kid_data._status || this->_kid_data._type)
        return 0;
    if (!this->_kid_data._free_times && this->_kid_data._energy <= 1)
        return 0;
    DATA_TYPE dt;
    KID_SYSTEM_CONFIG *cfg = (KID_SYSTEM_CONFIG *)world_manager::GetDataMan().get_data_ptr(KID_SYSTEM_CONFIG_ID, ID_SPACE_CONFIG, dt);

    if (!cfg || dt != DATA_TYPE::DT_KID_SYSTEM_CONFIG)
        return 0;

    // for ( i = 0; i <= 4; ++i )
    // {
    //   tmp = abase::Rand(0, 9999);
    //   tmp_p[i] = 0;
    //   for ( tmp_0 = 0; tmp_0 < 5; ++tmp_0 )
    //   {
    //     i_0 = cfg->unk[6 * this->_kid_data._pool_lvl + 14 + tmp_0];
    //     if ( tmp <= i_0 )
    //     {
    //       tmp_p[i] = tmp_0;
    //       break;
    //     }
    //     tmp -= i_0;
    //   }
    // }
    // for ( i_0a = 0; i_0a <= 4; ++i_0a )
    //   this->_kid_data._course_new[i_0a] = COURSE_ESSENCE_ID_61[tmp_p[i_0a]][abase::Rand(
    //                                                                           0,
    //                                                                           COURSE_ESSENCE_LV_61[tmp_p[i_0a]] - 1)];
    if (this->_kid_data._free_times)
        this->_kid_data._free_times = 0;
    else
        this->_kid_data._energy -= 2;
    return 1;
}

void player_kid::Heartbeat(int cur_time)
{
    if (this->_update_time)
    {
        if (cur_time >= this->_update_time)
        {
            this->_kid_data._new_day = 0;
            this->_update_time = 0;
            this->_kid_data._type = 0;
            ClientSync(1);
        }
    }
}

void player_kid::ClientSync(int type)
{
    printf("\nPlayer_kid.cpp class -> ClientSync Init type:%d", type);
    switch (type)
    {
    case 1:
    {
        printf("\nPlayer_kid.cpp class -> ClientSync DENTRO DO CASE 1");

        printf("\n=== kid data ===\n");
        printf("Name: %s\n", this->_kid_data._name);
        printf("Gender: %d\n", this->_kid_data._gender);
        printf("Pool Level: %d\n", this->_kid_data._pool_lvl);
        printf("Free Times: %d\n", this->_kid_data._free_times);
        printf("Energy: %d\n", this->_kid_data._energy);
        printf("Status: %d\n", this->_kid_data._status);
        printf("Type: %d\n", this->_kid_data._type);
        printf("New Day: %d\n", this->_kid_data._new_day);
        printf("Growth Days: %d\n", this->_kid_data._growth_days);
        printf("Exp: %d\n", this->_kid_data._exp);
        printf("Course New: ");
        for (int i = 0; i < 5; ++i)
        {
            printf("%d ", this->_kid_data._course_new[i]);
        }
        printf("\nCourse Inv: ");
        for (int i = 0; i < 14; ++i)
        {
            printf("%d ", this->_kid_data._course_inv[i]._tid);
        }
        printf("\nCourse Inv Lvl: ");
        for (int i = 0; i < 14; ++i)
        {
            printf("%d ", this->_kid_data._course_inv[i]._lvl);
        }
        printf("\n=== end ===\n");

        if (this->_kid_data._status) {
            _owner->_runner->send_kid_val1(reinterpret_cast<char *>(&this->_kid_data), 128);
        }

        printf("\nPlayer_kid.cpp class -> ClientSync FIM DENTRO DO CASE 1");
        break;
    }

    case 4:
        if (_select < 0)
            return;
        {
            // void* vtable = *reinterpret_cast<void**>(this);
            // void* func = reinterpret_cast<void**>(reinterpret_cast<char*>(vtable) + 0x67C)[0];
            // using Fn = void (*)(void*, int, int);
            // Fn(fn_cast) = reinterpret_cast<Fn>(func);
            // fn_cast(*reinterpret_cast<void**>(vtable + 0x10), _select, -1);
        }
        break;

    case 5:
    case 0xF:
    {
        abase::vector<unsigned int, abase::fast_alloc<4, 128>> addon_mask_data;
        addon_mask_data.push_back(0);
        addon_mask_data.push_back(0);

        int i = 0;
        int count_0 = 0;

        for (; i <= 5; ++i)
        {
            void *data = reinterpret_cast<void *>(reinterpret_cast<char *>(this) + 0x94 + i * 56);
            if (*reinterpret_cast<int *>(data) != 0)
            {
                addon_mask_data.push_back(i);

                int num = addon_mask_data.size();
                addon_mask_data.push_back(0);

                int count2 = 0;
                for (int j = 0; j <= 0x3F; ++j)
                {
                    uint64_t *ptr = reinterpret_cast<uint64_t *>(reinterpret_cast<char *>(this) + 0x1DC + 8 * i);
                    uint64_t value = *ptr;
                    if ((value >> j) & 1)
                    {
                        addon_mask_data.push_back(j);
                        ++count2;
                    }
                }

                addon_mask_data[num] = count2;
                ++count_0;
            }
        }

        addon_mask_data[1] = count_0;

        if (count_0 > 0)
        {
            void *vtable = *reinterpret_cast<void **>(this);
            void *func = reinterpret_cast<void **>(reinterpret_cast<char *>(vtable) + 0x6A8)[0];
            using Fn = void (*)(void *, void *, size_t);
            Fn(fn_cast) = reinterpret_cast<Fn>(func);
            fn_cast(*reinterpret_cast<void **>(vtable + 0x10), addon_mask_data.begin(), addon_mask_data.size());
        }
        break;
    }
    default:
        break;
    }
}

void player_kid::Save(base_wrapper &ar)
{
    // TODO: Serialize kid data
}

void player_kid::Load(base_wrapper &ar)
{
    // TODO: Deserialize kid data
}

void player_kid::Swap(player_kid &other)
{
    // TODO: Swap content with another player_kid instance
}

const void *player_kid::SaveToDB()
{
    // TODO: Save to persistent storage
    // *size = 520;
    return &this->_kid_data;
}

void player_kid::InitFromDB(const void *data, unsigned int size)
{
    spin_autolock l(this->_lock_data_map);
    // TODO: Load from persistent storage
    if (size == 520) {
        memcpy(&this->_kid_data, data, size);
        ActivateAllAddon();
    }
}
void player_kid::StartDay()
{
    // TODO: Daily reset/init
}

bool player_kid::OnCreateKid()
{
    // TODO: This function is not fully implemented in the original code.

    // if (this->field_83 == 0 || this->field_7D != 0)
    //     return false;

    // DATA_TYPE dt;
    // auto* cfg = world_manager::GetDataMan()->get_data_ptr(KID_QUALITY_CONFIG, 5, dt);
    // if (!cfg || dt != 0x167)
    //     return false;

    // for (int i = 0; i <= 3; ++i) {
    //     // Range checks
    //     int value_min = cfg->getFieldValue(i, 0x48); // offset from pattern
    //     int value_max = cfg->getFieldValue(i, 0x4C);

    //     int player_value = this->field_75;
    //     if (player_value < value_min || player_value > value_max)
    //         continue;

    //     const void* prob_table = cfg->getProbTable(this->field_74, i); // simulating pointer math
    //     int idx = abase::RandSelect(prob_table, 8, 8);

    //     int id = cfg->getItemId(this->field_74, i, idx); // offset 0x50
    //     auto* cfg2 = world_manager::GetDataMan()->get_data_ptr(id, ID_SPACE_ITEM, dt);
    //     if (!cfg2 || dt != 0x166)
    //         return false;

    //     if (cfg2->debris_type < 0 || cfg2->debris_type > 5) {
    //         GLog::log(3, "kid_debri_type ERR  id:%d   %d\n", id, cfg2->debris_type);
    //         return false;
    //     }

    //     if (cfg2->gender != this->field_74) {
    //         GLog::log(3, "gender ERR  id:%d   %d   %d\n", id, this->field_74, cfg2->gender);
    //         return false;
    //     }

    //     kid_data* kid = this->getKidSlot(cfg2->debris_type);  // located at this + 0x88 + debris_type * sizeof(kid_data)
    //     if (kid->count != 0) {
    //         uint32_t count = cfg2->count;
    //         this->AddDebri(cfg2->debris_type, count);
    //         if (count != cfg2->count)
    //             continue;

    //         auto* inv = gplayer_imp::GetInventory(this);
    //         if (inv->IsFull())
    //             return false;

    //         item_tag_t tag = {};
    //         auto* data = world_manager::GetDataMan()->generate_item_from_player(cfg2->id, &tag, 2);
    //         if (!data)
    //             return false;

    //         if (count > data->count)
    //             count = data->count;
    //         data->count = count;

    //         if (!inv->Push(*data))
    //             return false;

    //         gplayer_imp::FirstAcquireItem(this, data);

    //         // Possibly a script/event call
    //         auto vftable = this->vftable->some_field_10;
    //         if (vftable) {
    //             auto func = *(vftable + 0x678);
    //             func(vftable, data->id, 0, data->extra_value, count - data->extra_value);
    //         }

    //         FreeItem(data);
    //     } else {
    //         kid->exists = true;
    //         kid->field_4 = 0;
    //         kid->count = 0;
    //         kid->id = id;
    //     }

    //     this->UpdateKid(cfg2->debris_type);

    //     auto vftable = this->vftable->some_field_10;
    //     if (vftable) {
    //         auto func = *(vftable + 0x678);
    //         func(vftable, id, 0);
    //     }

    //     this->ClientSync(5);
    //     std::memset(reinterpret_cast<void*>(reinterpret_cast<uint8_t*>(this) + 4), 0, 0x80);
    // }

    return true;
}

bool player_kid::UpPool()
{
    if (*reinterpret_cast<uint8_t *>(reinterpret_cast<char *>(this) + 0x83) == 0 ||
        *reinterpret_cast<uint8_t *>(reinterpret_cast<char *>(this) + 0x82) != 0)
    {
        return false;
    }

    if (*reinterpret_cast<int *>(reinterpret_cast<char *>(this) + 0x7E) <= 3)
    {
        return false;
    }

    DATA_TYPE dt;
    KID_SYSTEM_CONFIG *cfg = (KID_SYSTEM_CONFIG *)world_manager::GetDataMan().get_data_ptr(KID_SYSTEM_CONFIG_ID, ID_SPACE_CONFIG, dt);

    if (!cfg || dt != DT_KID_SYSTEM_CONFIG)
        return false;

    if (*reinterpret_cast<int *>(reinterpret_cast<char *>(this) + 0x1A) >= *reinterpret_cast<const int *>(reinterpret_cast<const char *>(cfg) + 0x168))
    {
        return false;
    }

    *reinterpret_cast<int *>(reinterpret_cast<char *>(this) + 0x7E) -= 4;
    *reinterpret_cast<int *>(reinterpret_cast<char *>(this) + 0x1A) += 4;

    auto job = static_cast<int8_t>(*reinterpret_cast<uint8_t *>(reinterpret_cast<char *>(this) + 0x18));
    int job_index = job * 3;
    const int *max_ptr = reinterpret_cast<const int *>(reinterpret_cast<const char *>(cfg) + 0x90 + (job_index * 8));

    if (*reinterpret_cast<int *>(reinterpret_cast<char *>(this) + 0x1A) >= *max_ptr)
    {
        (*reinterpret_cast<uint8_t *>(reinterpret_cast<char *>(this) + 0x18))++;
    }

    return true;
}

void player_kid::BuyCourse(int id)
{
    // TODO: Buy a course for a kid
}

void player_kid::SellCourse(int id)
{
    // TODO: Sell a course
}

void player_kid::MoveCourse(int from, int to)
{
    // TODO: Move course from one slot to another
}

void player_kid::UpCourse(int kidId, int courseId, int level)
{
    // TODO: Upgrade a specific course
}

void player_kid::EndTeach()
{
    // TODO: Finalize teaching process
}

void player_kid::KidModify(int, const void *, unsigned int)
{
    // TODO: Modify kid data directly
}

void player_kid::KidDeubug(int index)
{
    // TODO: Debug info or manual override
}

void player_kid::UpdateKid(int index)
{
    // TODO: Trigger update for specific kid slot
}

void player_kid::UpKidLvl(unsigned int id, int level)
{
    // TODO: Level up a kid
}

void player_kid::UseDebri(unsigned int itemId, int amount, unsigned int kidId)
{
    // TODO: Consume debris for upgrades
}

void player_kid::AddDebri(unsigned int itemId, unsigned int &outResult)
{
    // TODO: Add debris to internal state
}

void player_kid::ActivateReward(unsigned int id, unsigned int kidId)
{
    // TODO: Activate specific reward
}

void player_kid::ActivateAllAddon()
{
    // TODO: Activate all available add-ons
}

void player_kid::Activate(int id)
{
    // TODO: Activate given feature or effect
}

void player_kid::Deactivate(int id)
{
    // TODO: Deactivate given feature or effect
}

void player_kid::ActivateTransform()
{
    // TODO: Enable transformation state
}

void player_kid::DeactivateTransform()
{
    // TODO: Disable transformation state
}

unsigned int player_kid::NEXT_DAY_TIME()
{
    // TODO: Return next day timestamp
    return 0;
}
