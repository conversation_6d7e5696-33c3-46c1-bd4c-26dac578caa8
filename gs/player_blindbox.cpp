#include <common/utf.h>
#include <threadpool.h>
#include <malloc.h>
#include <unordered_map>
#include <db_if.h>
#include "threadusage.h"
#include "world.h"
#include "worldmanager.h"
#include "arandomgen.h"
#include "threadusage.h"
#include "player_imp.h"
#include "usermsg.h"
#include "public_quest.h"
#include "luamanager.h"
#include "player_blindbox.h"

BlindBoxConfig* config = BlindBoxConfig::GetInstance();
BlindBoxConfig* BlindBoxConfig::instance = nullptr;


BlindBoxConfig::BlindBoxConfig()
{
    enable = false;
    id = 0;
}

void BlindBoxConfig::Init()
{
    // Inicialização global, se necessário. Pode ficar vazio.
}

bool BlindBoxConfig::IsEnable(unsigned int table) const
{
    DATA_TYPE dt;
    BLINDBOX_CONFIG* cfg = (BLINDBOX_CONFIG*)world_manager::GetDataMan().get_data_ptr(table, ID_SPACE_CONFIG, dt);
    return (cfg && dt == DT_BLINDBOX_CONFIG);
}

float BlindBoxConfig::GetOnceProbability() const
{
    // Retorne a probabilidade correta
    return 0.0f;
}

float BlindBoxConfig::GetAllProbability() const
{
    return 0.0f;
}

int BlindBoxConfig::GetOnceConsume(unsigned int table) const
{
    DATA_TYPE dt;
    BLINDBOX_CONFIG* cfg = (BLINDBOX_CONFIG*)world_manager::GetDataMan().get_data_ptr(table, ID_SPACE_CONFIG, dt);
    if (!cfg || dt != DT_BLINDBOX_CONFIG)
        return 0;
    return cfg->once_consume;
}

int BlindBoxConfig::GetAllConsume(unsigned int table) const
{
    DATA_TYPE dt;
    BLINDBOX_CONFIG* cfg = (BLINDBOX_CONFIG*)world_manager::GetDataMan().get_data_ptr(table, ID_SPACE_CONFIG, dt);
    if (!cfg || dt != DT_BLINDBOX_CONFIG)
        return 0;
    return cfg->all_consume;
}

int BlindBoxConfig::GetCount() const
{
    return 0;
}

int BlindBoxConfig::GetNeedTime() const
{
    return 0;
}

INT64 BlindBoxConfig::GetStartTimeStamp() const
{
    return 0;
}

INT64 BlindBoxConfig::GetEndTimeStamp() const
{
    return 0;
}

void BlindBoxConfig::GetNormalItems(ITEM* items, unsigned int& count) const
{
    count = 0;
}

void BlindBoxConfig::GetLimitedItems(ITEM* items, unsigned int& count) const
{
    count = 0;
}

bool BlindBoxConfig::DrawOnce(unsigned int table, ITEM& item) const
{
    DATA_TYPE dt;
    BLINDBOX_CONFIG* cfg = (BLINDBOX_CONFIG*)world_manager::GetDataMan().get_data_ptr(table, ID_SPACE_CONFIG, dt);
    if (!cfg || dt != DT_BLINDBOX_CONFIG)
        return false;

    // Decide se será item limitado ou normal
    bool is_limited = (rand() % 100) < cfg->once_probability;

    // Seleciona o pool correto
    const void* pool_void = is_limited ? (const void*)cfg->limited_itens : (const void*)cfg->normal_itens;
    const decltype(cfg->normal_itens) *pool = (const decltype(cfg->normal_itens)*)pool_void;

    // Conta itens válidos
   unsigned int valid_count = 0;
    ITEM valid[MAX_ITEMS];
    for (unsigned int i = 0; i < MAX_ITEMS; ++i)
    {
        if ((*pool)[i].item_id > 0)
        {
            valid[valid_count].shop_index = (*pool)[i].shop_index;
            valid[valid_count].item_id = (*pool)[i].item_id;
            valid_count++;
        }
    }

    if (valid_count == 0)
        return false;

    // Sorteia um item válido
    unsigned int idx = rand() % valid_count;
    item = valid[idx];
    return true;
}

bool BlindBoxConfig::DrawAll(unsigned int table, unsigned int draw_count, ITEM *items, unsigned int &count) const
{
    count = 0;
    if (draw_count == 0 || !items)
        return false;

    DATA_TYPE dt;
    BLINDBOX_CONFIG *cfg = (BLINDBOX_CONFIG *)world_manager::GetDataMan().get_data_ptr(table, ID_SPACE_CONFIG, dt);
    if (!cfg || dt != DT_BLINDBOX_CONFIG)
        return false;

    // Monta pools
    unsigned int normal_count = 0, limited_count = 0;
    ITEM normal_pool[MAX_ITEMS], limited_pool[MAX_ITEMS];
    for (unsigned int i = 0; i < MAX_ITEMS; ++i)
    {
        if (cfg->normal_itens[i].item_id > 0)
        {
            normal_pool[normal_count].shop_index = cfg->normal_itens[i].shop_index;
            normal_pool[normal_count].item_id = cfg->normal_itens[i].item_id;
            normal_count++;
        }
        if (cfg->limited_itens[i].item_id > 0)
        {
            limited_pool[limited_count].shop_index = cfg->limited_itens[i].shop_index;
            limited_pool[limited_count].item_id = cfg->limited_itens[i].item_id;
            limited_count++;
        }
    }

    if (draw_count > normal_count + (limited_count > 0 ? 1 : 0))
        return false; // Não há itens suficientes

    // Decide se vai sortear limitado
    bool got_limited = (rand() % 100) < cfg->all_probability && limited_count > 0;

    if (got_limited)
    {
        // Sorteia 1 limitado
        unsigned int limited_idx = rand() % limited_count;
        items[0] = limited_pool[limited_idx];

        // Monta pool normal sem o item limitado sorteado (caso haja IDs iguais)
        ITEM temp_normal[MAX_ITEMS];
        unsigned int temp_count = 0;
        for (unsigned int i = 0; i < normal_count; ++i)
        {
            if (normal_pool[i].item_id != items[0].item_id)
                temp_normal[temp_count++] = normal_pool[i];
        }

        if (temp_count < draw_count - 1)
            return false;

        // Embaralha o pool normal
        for (unsigned int i = 0; i < temp_count; ++i)
        {
            unsigned int j = i + rand() % (temp_count - i);
            std::swap(temp_normal[i], temp_normal[j]);
        }

        // Sorteia os demais do pool normal
        for (unsigned int i = 0; i < draw_count - 1; ++i)
            items[i + 1] = temp_normal[i];

        count = draw_count;
        return true;
    }
    else
    {
        // Sorteia todos do pool normal, sem repetição
        if (normal_count < draw_count)
            return false;

        // Embaralha o pool normal
        for (unsigned int i = 0; i < normal_count; ++i)
        {
            unsigned int j = i + rand() % (normal_count - i);
            std::swap(normal_pool[i], normal_pool[j]);
        }

        for (unsigned int i = 0; i < draw_count; ++i)
            items[i] = normal_pool[i];

        count = draw_count;
        return true;
    }
}

bool BlindBoxConfig::GetAward(unsigned int table, unsigned int draw_count, unsigned int* indices, unsigned int* item_ids)
{
    if (!indices || !item_ids || draw_count == 0)
        return false;

    ITEM items[MAX_ITEMS];
    unsigned int count = 0;
    if (!DrawAll(table, draw_count, items, count))
        return false;

    DATA_TYPE dt;
    BLINDBOX_CONFIG* cfg = (BLINDBOX_CONFIG*)world_manager::GetDataMan().get_data_ptr(table, ID_SPACE_CONFIG, dt);
    if (!cfg || dt != DT_BLINDBOX_CONFIG)
        return false;

    for (unsigned int i = 0; i < count; ++i)
    {
        indices[i] = -1;
        for (unsigned int j = 0; j < MAX_ITEMS; ++j)
        {
            if (cfg->normal_itens[j].item_id == items[i].item_id ||
                cfg->limited_itens[j].item_id == items[i].item_id)
            {
                indices[i] = j;
                break;
            }
        }
        item_ids[i] = items[i].item_id;
    }
    return true;
}