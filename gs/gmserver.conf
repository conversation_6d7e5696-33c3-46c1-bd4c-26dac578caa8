;Only client-end will use this configure file
;Client is GameServer, the sessions below are linkservers that gameserver should connect
[GameServerID]
127.0.0.1		=	0
**********		=	1

[ProviderServers]
count			=	3
;name			=	DeliverServer,linkserver1,linkserver2

;this must be delivery server
[GProviderClient0]
;id				=	0
type			=	tcp
port			=	29300
address			=	************
so_sndbuf		=	32768
so_rcvbuf		=	32768
ibuffermax              =       32768
obuffermax              =       32768
tcp_nodelay		=	1
listen_backlog	=	10
accumulate		=	0

[GProviderClient1]
;id				=	1
type			=	tcp
port			=	29301
address			=	************
so_sndbuf		=	32768
so_rcvbuf		=	32768
ibuffermax              =       32768
obuffermax              =       32768
tcp_nodelay		=	1
listen_backlog	=	10
accumulate		=	0

[GProviderClient2]
type                    =       tcp
port                    =       29600
address                 =       ************
so_sndbuf               =       32768
so_rcvbuf               =       32768
ibuffermax              =       32768
obuffermax              =       32768
tcp_nodelay             =       1
listen_backlog  =       10
accumulate              =       0

[GamedbClient]
type                    =	tcp
port                    =	29400
address                 =	************
;port                    =	29400
;address                 =	************
so_sndbuf               =	16384
so_rcvbuf               =	16384
ibuffermax              =       16384
obuffermax              =       16384
;so_broadcast		= 	1
tcp_nodelay             =	1
;isec                   =	2
;iseckey                =	123
;osec                   =	2
;oseckey                =	456

[LogclientClient]
type                    =       udp
port                    =       11100
;address                        =       **************
address                 =       **********
so_sndbuf               =       16777216
so_rcvbuf               =       16384
accumulate              =       16777216

[LogclientTcpClient]
type                    =       tcp
port                    =       11101
;address                        =       **************
address                 =       **********
so_sndbuf               =       16777216
so_rcvbuf               =       16384
accumulate              =       16777216

[ThreadPool]
threads			=	(1,2)(100,1)(101,1)(0,1)
max_queuesize		=	1048576
prior_strict            =       1


