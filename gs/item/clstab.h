#ifndef __ONLINEGAME_GS_CLS_TAB_H__
#define __ONLINEGAME_GS_CLS_TAB_H__

enum
{
	CLS_PLAYER_IMP,					//0
	CLS_PLAYER_IMP_LESS_10,				//1  �Ѿ�����
	C<PERSON>_PLAYER_CONTROLLER,				//2	
	C<PERSON>_PLAYER_CONTROLLER_LESS_10,			//3  �Ѿ�����
	CLS_PLAYER_DISPATCHER,				//4
	CLS_NPC_IMP,					//5
	C<PERSON>_NPC_CONTROLLER,				//6
	
	CLS_NPC_DISPATCHER,				//7
	CLS_MATTER_IMP,					//8
	CLS_MATTER_DISPATCHER,				//9
	CLS_MATTER_CONTROLLER,				//10
	C<PERSON>_MATTER_ITEM_BASE_IMP,			//11
	CLS_MATTER_ITEM_DISPATCHER,			//12
	CLS_GM_DISPATCHER,				//13
	CLS_PVP_PLAYER_IMP,				//14
	CLS_PET_IMP,					//15
	C<PERSON>_PET_DISPATCHER,				//16
	CLS_BATTLEGROUND_PLAYER_IMP,			//17
	CLS_PET_IMP_2,					//18
	
	CLS_MATTER_ITEM_IMP,
	C<PERSON>_MATTER_ITEM_CONTROLLER,
	CLS_MATTER_MONEY_IMP,
	CLS_MATTER_MINE_IMP,
	CLS_MATTER_DYN_IMP,
	CLS_NPC_AI_DEFAULT,	
	CLS_NPC_AI_POLICY_BASE,
	CLS_NPC_AI_POLICY_MASTER,
	CLS_NPC_AI_POLICY_MINOR,
	CLS_NPC_AI_POLICY_BOSS,
	CLS_NPC_AI_TASK,
	CLS_NPC_AI_MELEE_TASK,
	CLS_NPC_AI_RANGE_TASK,
	CLS_NPC_AI_MAGIC_TASK,
	CLS_NPC_AI_MAGIC_MELEE_TASK,
	CLS_NPC_AI_FOLLOW_MASTER_TASK,
	CLS_NPC_AI_PET_FOLLOW_MASTER_TASK,
	CLS_NPC_AI_FOLLOW_TARGET_TASK,
	CLS_NPC_AI_RUNAWAY_TASK,
	CLS_NPC_AI_SILIENT_RUNAWAY_TASK,
	CLS_NPC_AI_SILIENT_TASK,
	CLS_NPC_AI_SKILL_TASK,
	CLS_NPC_AI_SKILL_TASK_2,
	CLS_NPC_AI_PET_SKILL_TASK,
	CLS_NPC_AI_REST_TASK,
	CLS_NPC_AI_REGENERATION_TASK,
	CLS_NPC_AI_PATROL_TASK,
	CLS_NPC_AI_POLICY_PATROL,
	CLS_NPC_AI_FIX_MELEE_TASK,
	CLS_NPC_AI_RETURNHOME_TASK,
	CLS_NPC_AI_FIX_MAGIC_TASK,
	CLS_NPC_AI_POLICY_PET,

	CLS_NPC_AGGRO_POLICY,
	CLS_NPC_MINOR_AGGRO_POLICY,
	CLS_SERVICE_NPC_IMP,
	CLS_NPC_AI_POLICY_GUARD,
	CLS_NPC_AI_POLICY_TURRET,
	CLS_PET_AGGRO_POLICY,
	CLS_TURRET_AGGRO_POLICY,

	CLS_NPC_GUARD_AGENT,
	CLS_NPC_PATROL_AGENT,
	CLS_NPC_BASE_PATROL_AGENT,

	CLS_PLANT_PET_IMP,
	CLS_NPC_AI_POLICY_PLANT_PET,
	CLS_FACTIONFORTRESS_PLAYER_IMP,		//63	
	CLS_NPC_AI_PLAY_ACTION_TASK,
	CLS_COUNTRYBATTLE_PLAYER_IMP,		//65
	CLS_SWITCH_ADDITIONAL_DATA,
	CLS_COUNTRYTERRITORY_SWITCH_DATA,
	CLS_MOBILE_PLAYER_IMP,				//68
	CLS_BATTLE_BASE_PLAYER_IMP,
	CLS_TRICKBATTLE_PLAYER_IMP,			//70
	CLS_TRICKBATTLE_SWITCH_DATA,
	CLS_MNFACTION_SWITCH_DATA,          //72
	CLS_MNFACTION_PLAYER_IMP,           
	CLS_SINGLE_ARENA_IMP, //74

	CLS_BATTLE_ARENA_IMP = 80,
	CLS_MATTER_MONEY2_IMP = 81,
	
	CLS_SESSION_BASE = 100,
	CLS_SESSION_EMPTY,
	CLS_SESSION_MOVE,
	CLS_SESSION_STOP_MOVE,
	CLS_SESSION_NORMAL_ATTACK,
	CLS_SESSION_NPC_ZOMBIE,
	CLS_SESSION_NPC_ATTACK,
	CLS_SESSION_NPC_RANGE_ATTACK,
	CLS_SESSION_NPC_KEEP_OUT,
	CLS_SESSION_NPC_DELAY,
	CLS_SESSION_NPC_FLEE,
	CLS_SESSION_NPC_SILENT_FLEE,
	CLS_SESSION_NPC_FOLLOW_TARGET,
	CLS_SESSION_NPC_EMPTY,
	CLS_SESSION_NPC_CRUISE,
	CLS_SESSION_SKILL,
	CLS_SESSION_PRODUCE,
	CLS_SESSION_PRODUCE2,
	CLS_SESSION_DECOMPOSE,
	CLS_SESSION_CANCEL_ACTION,
	CLS_SESSION_USE_ITEM,
	CLS_SESSION_USE_ITEM_WITH_TARGET,
	CLS_SESSION_PLAYER_SIT_DOWN,
	CLS_SESSION_NPC_SKILL,
	CLS_SESSION_GATHER,
	CLS_SESSION_USE_TRASHBOX,
	CLS_SESSION_NPC_FOLLOW_MASTER,
	CLS_SESSION_EMOTE,
	CLS_SESSION_NPC_REGENERATION,
	CLS_SESSION_GATHER_PREPARE,
	CLS_SESSION_DEAD_MOVE,
	CLS_SESSION_DEAD_STOP_MOVE,
	CLS_SESSION_RESURRECT,
	CLS_SESSION_RESURRECT_BY_ITEM,
	CLS_SESSION_RESURRECT_IN_TOWN,
	CLS_SESSION_NPC_PATROL,
	CLS_SESSION_COMPLETE_TRAVEL,
	CLS_SESSION_ENTER_SANCTUARY,
	CLS_SESSION_SAY_HELLO,
	CLS_SESSION_INSTANT_SKILL,
	CLS_SESSION_COSMETIC,
	CLS_SESSION_REGION_TRANSPORT,
	CLS_SESSION_RESURRECT_PROTECT,
	CLS_SESSION_POS_SKILL,
	CLS_SESSION_SUMMON_PET,
	CLS_SESSION_RECALL_PET,
	CLS_SESSION_FREE_PET,
	CLS_SESSION_RESTORE_PET,
	CLS_SESSION_RUNE_SKILL,
	CLS_SESSION_RUNE_INSTANT_SKILL,
	CLS_SESSION_PRODUCE3,
	CLS_SESSION_USE_USER_TRASHBOX,
	CLS_SESSION_KNOCKBACK,
	CLS_SESSION_TEST,
	CLS_SESSION_CONGREGATE,
	CLS_SESSION_ENGRAVE,
	CLS_SESSION_ADDONREGEN,
	CLS_SESSION_PULLOVER,
	CLS_SESSION_TELEPORT,
	CLS_SESSION_PRODUCE4,
	CLS_SESSION_ENTER_PK_PROTECTED,
	CLS_SESSION_REBUILD_PET_INHERITRATIO,
	CLS_SESSION_REBUILD_PET_NATURE,
	CLS_SESSION_KNOCKUP,
	CLS_SESSION_TELEPORT2,
	CLS_SESSION_PRODUCE5,
    CLS_SESSION_RESURRECT_BY_CASH,
	CLS_SESSION_OLD_ENGRAVE,
	CLS_SESSION_LIB_PRODUCE,
	CLS_SESSION_NEW_ENGRAVE,
	CLS_SESSION_EASY_PRODUCE,
	CLS_SESSION_NEW_ARMOR,	

	CLS_FILTER = 200,			//����filterҪ���̣����ֵ��Ҫ�� 
	CLS_FILTER_HEALING,
	CLS_FILTER_MANA_GEN,
	CLS_FILTER_SKILL_INTERRUPT,
	CLS_FILTER_FLYSWORD,
	CLS_FILTER_ANGEL_WING,
	CLS_FILTER_DEFENSE_RUNE,
	CLS_FILTER_RESISTANCE_RUNE,
	CLS_FILTER_INVINCIBLE,
	CLS_FILTER_INVINCIBLE_SPEC_ID,
	CLS_FILTER_SITDOWN,
	CLS_FILTER_PVP_LIMIT,
	CLS_FILTER_TRAVEL_MODE,
	CLS_FILTER_GATHER_INTERRUPT,
	CLS_FILTER_NPC_PASSIVE,
	CLS_FILTER_EFFECT,
	CLS_FILTER_BANISH_INVINCIBLE,
	CLS_FILTER_CHECK_INSTANCE_KEY,
	CLS_FILTER_PVP_DUEL,
	CLS_FILTER_MOUNT,
	CLS_FILTER_CHECK_BATTLEFIELD_KEY,
	CLS_FILTER_TOWER_BUILD,
	CLS_FILTER_PET_DAMAGE,
	CLS_FILTER_INVISIBLE,
	CLS_FILTER_SHIELDUSER,
	CLS_FILTER_GM_INVISIBLE,
	CLS_FILTER_WEDDING,
	CLS_FILTER_CHECK_FACTIONFORTRESS_KEY,
	CLS_FILTER_CHECK_KICKOUT,
	CLS_FILTER_ONLINE_AWARD_EXP,
	CLS_FILTER_PK_PROTECTED,
	CLS_FILTER_NONPENALTY_PVP,
	CLS_FILTER_CHECK_COUNTRYKICKOUT,
	CLS_FILTER_CHECK_COUNTRYBATTLE_KEY,
	CLS_FILTER_GTAWARD,
	CLS_FILTER_CHECK_TRICKBATTLE_KEY,
	CLS_FILTER_MOVING_SKILL_INTERRUPT,
	CLS_FILTER_INVINCIBLE_2,
	CLS_FILTER_CHECK_VISA,
	CLS_FILTER_CHECK_MNFACTION_KEY,
	CLS_FILTER_CHECK_SOLOCHALLENGE_KEY,

	CLS_ITEM = 400,
	CLS_ITEM_EQUIP,
	CLS_ITEM_WEAPON,
	CLS_ITEM_RANGE_WEAPON,
	CLS_ITEM_MELEE_WEAPON,
	CLS_ITEM_ARMOR,
	CLS_ITEM_PROJECTILE,
	CLS_ITEM_DECORATION,
	CLS_ITEM_TOSSMATTER,
	CLS_ITEM_TOWNSCROLL,
	CLS_ITEM_TOWNSCROLL2,
	CLS_ITEM_DAMAGE_RUNE,
	CLS_ITEM_DEFENSE_RUNE,
	CLS_ITEM_RESISTANCE_RUNE,
	CLS_ITEM_STONE,
	CLS_ITEM_FLY_SWORD,
	CLS_ITEM_ANGEL_WING,
	CLS_ITEM_HEALING_POTION,
	CLS_ITEM_MANA_POTION,
	CLS_ITEM_REJUVENATION_POTION,
	CLS_ITEM_HALF_ANTIDOTE,
	CLS_ITEM_FULL_ANTIDOTE,
	CLS_ITEM_TASKDICE,
	CLS_ITEM_CLS_FLY_SWORD,
	CLS_ITEM_FASHION_ITEM,
	CLS_ITEM_SKILLTOME,
	CLS_ITEM_FACEPILL,
	CLS_ITEM_FACE_TICKET,
	CLS_ITEM_RESURRECT_SCROLL,
	CLS_ITEM_MOBGEN,
	CLS_ITEM_PET_EGG,
	CLS_ITEM_PET_FOOD,
	CLS_ITEM_CONTROLLER,
	CLS_ITEM_FIREWORKS,
	CLS_ITEM_SKILL_TRIGGER,
	CLS_ITEM_DUMMY,
	CLS_ITEM_BUGLE,
	CLS_ITEM_BIBLE,
	CLS_ITEM_HP_AMULET,
	CLS_ITEM_MP_AMULET,
	CLS_ITEM_DBL_EXP,
	CLS_ITEM_ELF,	//lgc
	CLS_ITEM_ELF_EQUIP,
	CLS_ITEM_ELF_EXPPILL,
	CLS_ITEM_STALLCARD,
	CLS_ITEM_SKILLTRIGGER2,
	CLS_ITEM_QUERYOTHERPROPERTY,
	CLS_ITEM_INCSKILLABILITY,
	CLS_ITEM_WEDDING_BOOKCARD,
	CLS_ITEM_WEDDING_INVITECARD,
	CLS_ITEM_SHARPENER,
	CLS_ITEM_CONGREGATE,
	CLS_ITEM_FORCE_TICKET,
	CLS_ITEM_DYNSKILL,
	CLS_ITEM_GENERALCARD,
	CLS_ITEM_GENERALCARD_DICE,
	CLS_ITEM_SOUL,
	CLS_ITEM_ASTROLABE,
	CLS_ITEM_OCCUP_PACKAGE,
	CLS_ITEM_FIREWORKS2,
	CLS_ITEM_FIX_POSITION_TRANSMIT,
	
	CLS_ITEM_EASYPRODUCE,
	CLS_ITEM_UNLOCK_RUNE_SLOT_ITEM_ESSENCE,
	CLS_ITEM_RUNE_ITEM_ESSENCE,
	CLS_ITEM_FIX_MONSTER,
	CLS_ITEM_USED_FOR_AREA,
	CLS_ITEM_CAPTURE,
	CLS_ITEM_BIBLE_REFINE_TICKET,
	CLS_ITEM_REFINE_TICKET,
	CLS_ITEM_NEW_ARMOR,
	CLS_ITEM_QIHUN,
	CLS_ITEM_QILING,
	CLS_ITEM_QIHUN_COVER,
	CLS_ITEM_SLIDE_SKILL,
	CLS_ITEM_USE_FOR_SELF,
	CLS_ITEM_CAMP_TOKEN,
	CLS_ITEM_CAMP_TOKEN_UPGRADE,
	CLS_ITEM_CAMP_TOKEN_ROLLBACK,
	CLS_ITEM_CAMP_TOKEN_PROB_ADJUST,
	CLS_ITEM_FASHION_NEW_ITEM,
	CLS_ITEM_ILLUSTRATED_FASHION,
	CLS_ITEM_ILLUSTRATED_WING_EGG,
	CLS_ITEM_ILLUSTRATED_PET_EGG,
	CLS_ITEM_FAST_PRODUCE,
	CLS_ITEM_KID_DEBRIS,
	CLS_ITEM_KID_DEBRIS_GENERATOR,
	CLS_ITEM_MATERIAL_REFINE,
	CLS_ITEM_MATERIAL_REFINE_TICKET,
	CLS_ITEM_RED_BOOK_UPDATE_ITEM,
	
	CLS_USER_DEFINE = 0x400,
	CLS_SKILL_FILTER_BASE = 4096,
	CLS_SKILL_FILTER_SOLO_INVINCIBLE = 4405,
	CLS_SKILL_FILTER_END = 8192,
};
#endif
