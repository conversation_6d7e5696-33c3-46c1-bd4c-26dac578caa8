#ifndef __PLAYER_BLINDBOX_H__
#define __PLAYER_BLINDBOX_H__

class BlindBoxConfig
{
public:
    enum { MAX_ITEMS = 32 };

    struct ITEM
    {
        unsigned int shop_index;
        unsigned int item_id;
    };

private:
    bool enable;
    unsigned int id;

public:
    BlindBoxConfig();
    void Init();
    void Init(unsigned int table);
    bool IsEnable(unsigned int table) const;

    // Métodos para acessar informações da configuração
    float GetOnceProbability() const;
    float GetAllProbability() const;
    int GetOnceConsume(unsigned int table) const;
    int GetAllConsume(unsigned int table) const;
    int GetCount() const;
    int GetNeedTime() const;
    INT64 GetStartTimeStamp() const;
    INT64 GetEndTimeStamp() const;
    void GetNormalItems(ITEM* items, unsigned int& count) const;
    void GetLimitedItems(ITEM* items, unsigned int& count) const;

    // Métodos de sorteio
    bool DrawOnce(unsigned int table, ITEM& item) const;
    bool DrawAll(unsigned int table, unsigned int draw_count, ITEM* items, unsigned int& count) const;
    bool GetAward(unsigned int table, unsigned int draw_count, unsigned int* indices, unsigned int* item_ids);

 
     static BlindBoxConfig * GetInstance()
	{
		if (!instance)
		instance = new BlindBoxConfig();
		return instance;
	}
	static BlindBoxConfig * instance;
};

#endif // __PLAYER_BLINDBOX_H__