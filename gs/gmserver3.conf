;Only client-end will use this configure file
;Client is GameServer, the sessions below are linkservers that gameserver should connect
[GameServerID]
127.0.0.1		=	0
**********		=	1

[ProviderServers]
count			=	2
;name			=	DeliverServer,linkserver1,linkserver2

;this must be delivery server
[GProviderClient0]
;id				=	0
type			=	tcp
port			=	29300
address			=	************
so_sndbuf		=	16384
so_rcvbuf		=	16384
tcp_nodelay		=	1
listen_backlog	=	10
accumulate		=	0

[GProviderClient1]
;id				=	1
type			=	tcp
port			=	29301
address			=	************
so_sndbuf		=	16384
so_rcvbuf		=	16384
tcp_nodelay		=	1
listen_backlog	=	10
accumulate		=	0

[GProviderClient2]
;id				=	2
type			=	tcp
port			=	9301
address			=	127.0.0.1
so_sndbuf		=	16384
so_rcvbuf		=	16384
tcp_nodelay		=	1
listen_backlog	=	10
;isec			=	2
;iseckey		=	456
;osec			=	2
;oseckey		=	123

[GamedbClient]
type                    =	tcp
port                    =	29400
address                 =	************
so_sndbuf               =	16384
so_rcvbuf               =	16384
;so_broadcast		= 	1
tcp_nodelay             =	1
;isec                   =	2
;iseckey                =	123
;osec                   =	2
;oseckey                =	456

[ThreadPool]
threads			=	(1,5)(100,1)
max_queuesize		=	1048576
prior_strict            =       1


