#include "TaskProcess.h"
#include "taskman.h"
#include "../world.h"
#include "../player_imp.h"
#include "../template/itemdataman.h"
#include "../antiwallow.h"
#include <arandomgen.h>
#include <gsp_if.h>
#include "../public_quest.h"
#include "../global_controller.h"

static int quest_timer = 0;
static void QuestTimerRoutine(int index,void *object,int remain)
{
	quest_timer ++;
	if(quest_timer > 12)
	{
		OnTaskCheckAllTimeLimits(g_timer.get_systime());
		quest_timer = 0; 
	}
}

bool InitQuestSystem(const char * filename,const char * filename2,elementdataman * pMan)
{
	bool bRst = LoadTasksFromPack(pMan,filename,filename2);
	if(!bRst) return false;
	g_timer.set_timer(5*20,10,0,QuestTimerRoutine,NULL);
	return true;
}

static int pq_tick_counter = 0;
void PublicQuestRunTick()
{
	if(++pq_tick_counter >= 100)	//5����һ��heartbeat,���ʱ�䲻������Ķ�
	{
		pq_tick_counter = 0;
		world_manager::GetPublicQuestMan().Heartbeat(5);
		OnTaskPublicQuestHeartbeat();
	}
}

unsigned int 
PlayerTaskInterface::GetPlayerLevel()
{
	return _imp->_basic.level;
}

void* 
PlayerTaskInterface::GetActiveTaskList()
{
	return _imp->_active_task_list.begin();
}

void* 
PlayerTaskInterface::GetFinishedTaskList()
{
	return _imp->_finished_task_list.begin();
}

void*
PlayerTaskInterface::GetFinishedTimeList()
{
	return _imp->_finished_time_task_list.begin();
}

void*
PlayerTaskInterface::GetFinishedCntList()
{
	return _imp->_finish_task_count_list.begin();
}

void* 
PlayerTaskInterface::GetStorageTaskList()
{
	return _imp->_storage_task_list.begin();
}

unsigned int* 
PlayerTaskInterface::GetTaskMask()
{
	return &_imp->_task_mask;
}

void 
PlayerTaskInterface::DeliverGold(unsigned int ulGoldNum)
{
	int silver = ulGoldNum;
	if(world_manager::AntiWallow())
	{
		anti_wallow::AdjustTaskMoney(_imp->_wallow_level, silver);
	}

	// Verifica Moedas
	if(_imp->GetSilverAmount() + silver >= TRASHBOX_MONEY_CAPACITY)
	{
		silver = TRASHBOX_MONEY_CAPACITY - _imp->GetSilverAmount();
	}
		
	//_imp->GainSilverWithDrop(silver,true);
	_imp->GainSilver(silver);
	_imp->_runner->task_deliver_money((signed)silver,_imp->GetSilver(), 0);	
}

void 
PlayerTaskInterface::DeliverExperience(unsigned int ulExp)
{
	int exp = ulExp & 0x3FFFFFFF;
	int sp = 0;
	if(world_manager::AntiWallow())
	{
		anti_wallow::AdjustTaskExpSP(_imp->_wallow_level, exp, sp);
	}
	_imp->ReceiveTaskExp(exp, sp);
}

void 
PlayerTaskInterface::DeliverSP(unsigned int ulSP)
{
	int exp = 0;
	int sp = ulSP&0x3FFFFFFF;
	if(world_manager::AntiWallow())
	{
		anti_wallow::AdjustTaskExpSP(_imp->_wallow_level, exp, sp);
	}
	_imp->ReceiveTaskExp(exp, sp);
}

void 
PlayerTaskInterface::DeliverReputation(int lReputation)
{
	int rep = _imp->GetReputation();
	rep += lReputation;
	_imp->SetReputation(rep);
	_imp->_runner->task_deliver_reputaion((int)lReputation,rep);
}


int 
PlayerTaskInterface::GetTaskItemCount(unsigned int ulTaskItem)
{
	item_list & inv = _imp->GetTaskInventory();
	int rst;
	if((rst = inv.Find(0,ulTaskItem)) >=0)
	{
		//�ǲ���Ӧ���ۼ�����λ�õ���Ŀ��
		return inv[rst].count;
	}
	return 0;
}

int 
PlayerTaskInterface::GetCommonItemCount(unsigned int ulCommonItem)
{
	item_list & inv = _imp->GetInventory();
	int rst = 0;
	int count = 0;
	while((rst = inv.Find(rst,ulCommonItem)) >=0)
	{
		//�ǲ���Ӧ���ۼ�����λ�õ���Ŀ��
		count += inv[rst].count;
		rst ++;
	}
	return count;
}

	
bool 
PlayerTaskInterface::IsInFaction(unsigned int)
{
	return ((gplayer*)_imp->_parent)->id_mafia;
}

int 
PlayerTaskInterface::GetFactionRole()
{
	return ((gplayer*)_imp->_parent)->rank_mafia;
}

/*
bool
PlayerTaskInterface::GoldConvert(unsigned int ulNum)
{
	return _imp->CheckSilver(ulNum);
}
*/

// Remove Moedas de Prata
unsigned int 
PlayerTaskInterface::GetGoldNum()
{
	return _imp->GetSilverAmount();
}

void 
PlayerTaskInterface::TakeAwayGold(unsigned int ulNum)
{
	if(GetGoldNum() < ulNum)
	{
		_imp->_runner->error_message(S2C::ERR_OUT_OF_FUND);
		return;
	}

	_imp->SpendSilverMoney(ulNum,true);
	_imp->SelfPlayerMoney();
}

// Remove Moedas de Ouro
unsigned int 
PlayerTaskInterface::GetGoldNumNew()
{
	return _imp->GetGoldAmount();
}

void 
PlayerTaskInterface::TakeAwayGoldNew(unsigned int ulNum)
{
	if(GetGoldNumNew() < ulNum)
	{
		_imp->_runner->error_message(S2C::ERR_OUT_OF_FUND);
		return;
	}

	_imp->SpendGoldMoney(ulNum,true);
	_imp->SelfPlayerMoney();
}

void 
PlayerTaskInterface::TakeAwayTaskItem(unsigned int ulTemplId, unsigned int ulNum)
{
	item_list &inv = _imp->GetTaskInventory();
	int rst = inv.Find(0,ulTemplId);
	if(rst >= 0 && inv[rst].count >= ulNum)
	{
		item& it = inv[rst];
		_imp->UpdateMallConsumptionDestroying(it.type, it.proc_type, ulNum);

		inv.DecAmount(rst,ulNum);
		//$$$$������Ϣ
		_imp->_runner->player_drop_item(gplayer_imp::IL_TASK_INVENTORY,rst,ulTemplId,ulNum,S2C::DROP_TYPE_TASK);
	}
}

void 
PlayerTaskInterface::TakeAwayCommonItem(unsigned int ulTemplId, unsigned int ulNum)
{
	item_list &inv = _imp->GetInventory();
	int rst = 0;
	unsigned int num = ulNum;
	while(num && (rst = inv.Find(rst,ulTemplId)) >= 0)
	{
		unsigned int count = num;
		if(inv[rst].count < count) count = inv[rst].count;

		item& it = inv[rst];
		_imp->UpdateMallConsumptionDestroying(it.type, it.proc_type, count);

		inv.DecAmount(rst,count);
		_imp->_runner->player_drop_item(gplayer_imp::IL_INVENTORY,rst,ulTemplId,count,S2C::DROP_TYPE_TASK);
		num -= count;
		rst ++;
	}
}

unsigned int 
TaskInterface::GetCurTime()
{
	return g_timer.get_systime();
}

void
TaskInterface::WriteLog(int nPlayerId, int nTaskId, int nType, const char* szLog)
{
	return GLog::tasklog(nPlayerId,nTaskId,nType,szLog);
}

void
TaskInterface::WriteKeyLog(int nPlayerId, int nTaskId, int nType, const char* szLog)
{
	return GLog::task(nPlayerId,nTaskId,nType,szLog);
}

int 
PlayerTaskInterface::GetReputation()
{
	return _imp->GetReputation();
}

unsigned int 
PlayerTaskInterface::GetCurPeriod()
{
	return _imp->_basic.sec.level;
}

unsigned int 
PlayerTaskInterface::GetCurPeriodFlag()
{
	return _imp->_basic.sec.flag;
}

void 
PlayerTaskInterface::SetCurPeriod(unsigned int per)
{
	_imp->SetSecLevel(per);
	if ( per >= 40 )
	{
		_imp->GetSkillAttr();
	}
}

unsigned int 
PlayerTaskInterface::GetPlayerId()
{
	return _imp->_parent->ID.id;
}

unsigned int 
PlayerTaskInterface::GetPlayerRace()
{
	return _imp->GetPlayerClass();
}

unsigned int 
PlayerTaskInterface::GetPlayerOccupation()
{
	return _imp->GetPlayerClass();
}

bool 
PlayerTaskInterface::CanDeliverCommonItem(unsigned int ulItemTypes)
{
	return _imp->GetInventory().GetEmptySlotCount() >= ulItemTypes;
}

void 
PlayerTaskInterface::DeliverCommonItem(unsigned int ulItem,unsigned int count, int lPeriod)
{
	if(count == 0) return;
	element_data::item_tag_t tag = {element_data::IMT_CREATE,0}; 
	item_data * pData = world_manager::GetDataMan().generate_item_for_drop(ulItem,&tag,sizeof(tag));
	if(pData)
	{
		if(count > pData->pile_limit) 
			pData->count = pData->pile_limit;
		else
			pData->count = count;
		if(lPeriod > 0 && pData->pile_limit == 1)
		{
			pData->expire_date = g_timer.get_systime() + lPeriod;
		}
		//����false����û��ȫ������
		if(_imp->ObtainItem(gplayer_imp::IL_INVENTORY,pData,true)) FreeItem(pData);
	}
	else
	{
		//��������ӡ����
	}
	world_manager::TestCashItemGenerated(ulItem, count);
}

bool 
PlayerTaskInterface::CanDeliverTaskItem(unsigned int ulItemTypes)
{
	return _imp->GetTaskInventory().GetEmptySlotCount() >= ulItemTypes;
}

void 
PlayerTaskInterface::DeliverTaskItem(unsigned int ulItem,unsigned int count)
{
	if(count == 0) return;
	element_data::item_tag_t tag = {element_data::IMT_CREATE,0}; 
	//$$$$$$$ �����Ƿ������������ʽ���?
	item_data * pData = world_manager::GetDataMan().generate_item_for_shop(ulItem,&tag,sizeof(tag));
	if(pData)
	{
		if(count > pData->pile_limit) 
			pData->count = pData->pile_limit;
		else
			pData->count = count;
		//����false����û��ȫ������
		if(_imp->ObtainItem(gplayer_imp::IL_TASK_INVENTORY,pData,true)) FreeItem(pData);
	}
	else
	{
		//��������ӡ����
	}
}

void 
PlayerTaskInterface::NotifyClient(const void* pBuf, unsigned int sz)
{
	_imp->_runner->send_task_var_data(pBuf,sz);
}

float 
PlayerTaskInterface::UnitRand()
{
	return abase::Rand(0.f,1.f);
}

int 
PlayerTaskInterface::RandNormal(int low, int high)
{
	return abase::RandNormal(low,high);
}

int  
PlayerTaskInterface::RandSelect(const float * option, int size)
{
	return abase::RandSelect(option,size);
}

int 
PlayerTaskInterface::GetTeamMemberNum()
{
	return _imp->GetTeamMemberNum();
}

void 
PlayerTaskInterface::NotifyPlayer(unsigned int ulPlayerId, const void* pBuf, unsigned int sz)
{
	XID id(GM_TYPE_PLAYER,ulPlayerId);
	_imp->SendTo<0>(GM_MSG_PLAYER_TASK_TRANSFER,id,0,pBuf,sz);
}

void 
PlayerTaskInterface::GetTeamMemberInfo(int nIndex, task_team_member_info* pInfo)
{
	const player_team::member_entry &ent = _imp->GetTeamMember(nIndex);
	pInfo->m_ulId = ent.id.id;
	pInfo->m_ulLevel = ent.data.level;
	pInfo->m_ulOccupation = ent.race & 0x7FFFFFFF;
	pInfo->m_bMale = !(ent.race & 0x80000000);
	pInfo->m_Pos[0] = ent.pos.x;
	pInfo->m_Pos[1] = ent.pos.y;
	pInfo->m_Pos[2] = ent.pos.z;
	pInfo->m_ulWorldTag = ent.data.world_tag;
	pInfo->m_ulWorldIndex = ent.data.plane_index;
	pInfo->m_iForce = ent.data.force_id;
	__PRINTF("MemberInfo index:%d id:%d pos:(%f,%f,%f)\n",nIndex,ent.id.id,ent.pos.x,ent.pos.y,ent.pos.z);
}

unsigned int  
PlayerTaskInterface::GetTeamMemberPos(int nIndex, float pos[3])
{
	const player_team::member_entry &ent = _imp->GetTeamMember(nIndex);
	pos[0] = ent.pos.x;
	pos[1] = ent.pos.y;
	pos[2] = ent.pos.z;
	return ent.data.world_tag;
}


bool PlayerTaskInterface::IsDeliverLegal()
{
	return _imp->IsDeliverLegal();
}

bool PlayerTaskInterface::IsCaptain()
{
	return _imp->IsTeamLeader();
}

bool PlayerTaskInterface::IsInTeam()
{
	return _imp->IsInTeam();
}


unsigned int PlayerTaskInterface::GetTeamMemberId(int nIndex)
{
	const player_team::member_entry &ent = _imp->GetTeamMember(nIndex);
	return ent.id.id;
}

bool PlayerTaskInterface::IsMale()
{
	return !_imp->IsPlayerFemale();

}

void PlayerTaskInterface::SetInventorySize(int lSize)
{       
	_imp->ChangeInventorySize(lSize);
}       

unsigned int 
PlayerTaskInterface::GetPos(float pos[3])
{
	const A3DVECTOR & selfpos = _imp->_parent->pos;
	pos[0] = selfpos.x;
	pos[1] = selfpos.y;
	pos[2] = selfpos.z;
	return world_manager::GetWorldTag();
}

bool PlayerTaskInterface::HasLivingSkill(unsigned int ulSkill)
{
	return _imp->GetSkillLevel(ulSkill) > 0;
	
}

int PlayerTaskInterface::GetLivingSkillProficiency(unsigned int ulSkill)
{
	return _imp->GetSkillAbility(ulSkill);
}

	
int PlayerTaskInterface::GetLivingSkillLevel(unsigned int ulSkill)
{
	return _imp->GetSkillLevel(ulSkill); 
	
}

void PlayerTaskInterface::SetNewRelayStation(unsigned int ulStationId)
{
	return _imp->ActivateWaypoint(ulStationId & 0xFFFF);
}

void PlayerTaskInterface::SetStorehouseSize(unsigned int ulSize)
{
	((gplayer_dispatcher*)_imp->_runner)->trashbox_capacity_notify(gplayer_imp::IL_TRASH_BOX, ulSize);
	_imp->IncTrashBoxChangeCounter();
	return _imp->_trashbox.SetTrashBoxSize(0,ulSize);
}

void PlayerTaskInterface::SetStorehouseSize2(unsigned int ulSize)
{
	((gplayer_dispatcher*)_imp->_runner)->trashbox_capacity_notify(gplayer_imp::IL_TRASH_BOX2, ulSize);
	_imp->IncTrashBoxChangeCounter();
	return _imp->_trashbox.SetTrashBoxSize(1,ulSize);
}

void PlayerTaskInterface::SetStorehouseSize3(unsigned int ulSize)
{
	((gplayer_dispatcher*)_imp->_runner)->trashbox_capacity_notify(gplayer_imp::IL_TRASH_BOX3, ulSize);
	_imp->IncTrashBoxChangeCounter();
	return _imp->_trashbox.SetTrashBoxSize(2,ulSize);
}

void PlayerTaskInterface::SetAccountStorehouseSize(unsigned int ulSize)
{
	ulSize *= 2;
	((gplayer_dispatcher*)_imp->_runner)->trashbox_capacity_notify(gplayer_imp::IL_USER_TRASH_BOX, ulSize);
	_imp->IncUserTrashBoxChangeCounter();
	return _imp->_user_trashbox.SetTrashBoxSize(0,ulSize);
}

void PlayerTaskInterface::AddDividend(int nDividend)
{
	GMSV::SendTaskReward(_imp->_parent->ID.id, nDividend);
}

void PlayerTaskInterface::SetFuryUpperLimit(unsigned int ulValue)
{
	_imp->SetMaxAP((int)ulValue);
}

void PlayerTaskInterface::TransportTo(unsigned int ulWorldId, const float pos[3], int lController)
{
	_imp->LongJump(A3DVECTOR(pos[0],pos[1],pos[2]),ulWorldId, lController);
}

void PlayerTaskInterface::GetSpecailAwardInfo(special_award* p)
{
	unsigned int param;
	_imp->GetSpecailTaskAward(p->id1, p->id2, param, p->special_mask);
}

void PlayerTaskInterface::SetPetInventorySize(unsigned int ulSize)
{
	_imp->SetPetSlotCapacity(ulSize);
}

bool PlayerTaskInterface::IsGM()
{
	return _imp->CheckGMPrivilege();
}

void PlayerTaskInterface::SetMonsterController(int id, bool bTrigger)
{
	if(id <= 0) return;
	world * pPlane = _imp->_plane;
	
	//��������ʵ��
	if(bTrigger)
	{
		pPlane->TriggerSpawn(id);
	}
	else
	{
		pPlane->ClearSpawn(id);
	}
}


// ��������ȫ������
void TaskUpdateGlobalData( unsigned int ulTaskId, const unsigned char pData[TASK_GLOBAL_DATA_SIZE])
{
	GMSV::SetTaskData((int)ulTaskId,pData,TASK_GLOBAL_DATA_SIZE);
}

void TaskQueryGlobalData( unsigned int ulTaskId, unsigned int ulPlayerId, const void* pPreservedData, unsigned int size)
{
	GMSV::GetTaskData((int)ulTaskId, (int)ulPlayerId, pPreservedData,size);
}

bool PlayerTaskInterface::IsMarried()
{
	return _imp->IsMarried();
}

bool PlayerTaskInterface::IsWeddingOwner()
{
	return _imp->_filters.IsFilterExist(FILTER_INDEX_WEDDING) && world_manager::GetInstance()->WeddingCheckOngoing(_imp->_parent->ID.id);	
}

void PlayerTaskInterface::CheckTeamRelationship(int nReason)
{
	return _imp->DoTeamRelationTask(nReason);
}

bool PlayerTaskInterface::HasIconStateID(unsigned int ulIconStateID)
{
	return _imp->IsExistTeamVisibleState(ulIconStateID);
}

void PlayerTaskInterface::OnTowerTaskDeliver(bool bSuccess)
{
	_imp->PlayerSoloChallengeStartTask(bSuccess);
}

void PlayerTaskInterface::OnTowerTaskComplete(bool bSuccess)
{
	_imp->PlayerSoloChallengeStageComplete(bSuccess);
}

void PlayerTaskInterface::DeliverSoloTowerChallengeScore(int score)
{
	_imp->PlayerDeliverSoloChallengeScore(score);
}

int PlayerTaskInterface::GetVIPLevel()
{
	return _imp->GetCashVipLevel();
}

bool PlayerTaskInterface::GetPointsEventTask(unsigned int uTaskID)
{
	return _imp->GetPointsEventTask(uTaskID);
}

void
PlayerTaskTeamInterface::SetMarriage(int nPlayer)
{
	marriage_op = 1;
	if(couple[0] == 0)
	{
		couple[0] = nPlayer;
	}
	else if(couple[1] == 0)
	{
		couple[1] = nPlayer;
	}
}

void PlayerTaskTeamInterface::Execute(gplayer ** list, unsigned int count)
{
	//$$$$$$$$$$��־        
	if(count != 2) return;
	gplayer_imp * pImp1 = (gplayer_imp*)list[0]->imp;
	gplayer_imp * pImp2 = (gplayer_imp*)list[1]->imp;
	if((list[0]->ID.id == couple[0] && list[1]->ID.id == couple[1]) ||
			(list[1]->ID.id == couple[0] && list[0]->ID.id == couple[1]))
	{
		//������Ϣ�����ݿ��delivery
		GMSV::SetCouple(couple[0],couple[1], 1);
		GDB::set_couple(couple[0],couple[1], 1);
		if(list[0]->ID.id == couple[0])
		{
			pImp1->SetSpouse(couple[1]);
			pImp2->SetSpouse(couple[0]);
		}
		else
		{
			pImp1->SetSpouse(couple[0]);
			pImp2->SetSpouse(couple[1]);
		}
		pImp1->_runner->player_change_spouse(pImp1->GetSpouse());
		pImp2->_runner->player_change_spouse(pImp2->GetSpouse());

		//�����ܹ����̵�ԭ����ǰ������ˣ�һ������normal״̬
		ASSERT(pImp1->IsDeliverLegal());
		ASSERT(pImp2->IsDeliverLegal());
		pImp1->ExternSaveDB();
		pImp2->ExternSaveDB();
	}
}

void PlayerTaskInterface::Divorce()
{
	if(!_imp->IsMarried()) return;
	GMSV::SetCouple(_imp->_parent->ID.id, _imp->GetSpouse(),0);
	_imp->SetSpouse(0);
	_imp->_runner->player_change_spouse(_imp->GetSpouse());
	return ;
}

/*156+*/
void PlayerTaskInterface::LeaveCarrier()
{
	gplayer_imp * pImp = (gplayer_imp *)_imp;
	pImp->LeaveCarrier(pImp->GetCarrierStorage().npcid, pImp->GetCarrierStorage().pos);
	return ;
}

/*160+*/
void PlayerTaskInterface::UnlockRepositorySystem()
{
	gplayer_imp * pImp = (gplayer_imp *)_imp;
	pImp->UnlockRepositorySystem();
	return ;
}

// Novo Sistema Exclusivo
void PlayerTaskInterface::SetAnecdotePoints()
{
	gplayer_imp * pImp = (gplayer_imp *)_imp;
	//pImp->SetAnecdotePoints();
	return ;
}

/*174*/
bool PlayerTaskInterface::CheckEventValidTask(int task_id)
{
	gplayer_imp * pImp = (gplayer_imp *)_imp;
	return pImp->CheckEventValidTask(task_id);
}

/*174*/
void PlayerTaskInterface::TaskExpCelestialMemorial(int swallow_exp)
{
	gplayer_imp * pImp = (gplayer_imp *)_imp;
	pImp->TaskExpCelestialMemorial(swallow_exp);
	return ;
}

void
PlayerTaskInterface::SendMessage(unsigned int task_id, int channel, int param)
{
	_imp->TaskSendMessage(task_id, channel, param);
}

int PlayerTaskInterface::GetGlobalValue(int lKey)
{
	world * pPlane = _imp->_plane;
	if(!pPlane) return 0;
	return pPlane->GetCommonValue(lKey);
	
}

void PlayerTaskInterface::PutGlobalValue(int lKey, int lValue)
{
	world * pPlane = _imp->_plane;
	if(!pPlane) return;
	pPlane->SetCommonValue(lKey, lValue);
}

void PlayerTaskInterface::ModifyGlobalValue(int lKey, int lValue)
{
	world * pPlane = _imp->_plane;
	if(!pPlane) return;
	pPlane->ModifyCommonValue(lKey, lValue);
}

int PlayerTaskInterface::SummonMonster(int mob_id, int count, int distance, int remain_time, bool die_with_self)
{
	if(count > 20) count = 20;
	npc_template * pTemplate = npc_stubs_manager::Get(mob_id);
	if(!pTemplate) return -1; 
	if(pTemplate->mine_info.is_mine)
		return _imp->SummonMine(mob_id,count,_imp->_parent->ID,distance,remain_time);
	else if(pTemplate->npc_data.is_npc)
		return _imp->SummonNPC(mob_id,count,_imp->_parent->ID,distance,remain_time);
	else
		return _imp->SummonMonster(mob_id,count,_imp->_parent->ID,distance,remain_time,(die_with_self?0x02:0),0);	
}

bool PlayerTaskInterface::IsShieldUser()
{
	gplayer* player = (gplayer*)(_imp->_parent);
	return player->object_state & gactive_object::STATE_SHIELD_USER;
}

void PlayerTaskInterface::SetAwardDeath(bool bDeadWithLoss)
{
	_imp->SendTo<0>(GM_MSG_DEATH, _imp->_parent->ID, bDeadWithLoss?1:2);
	return;	
}

unsigned int PlayerTaskInterface::GetRoleCreateTime()
{
	return _imp->GetCreateTime();
}

unsigned int PlayerTaskInterface::GetRoleLastLoginTime()
{
	return _imp->GetLastLoginTime();
}

unsigned int PlayerTaskInterface::GetAccountTotalCash()
{
	return _imp->GetMallCashAdd();
}
	
unsigned int PlayerTaskInterface::GetSpouseID()
{
	return ((gplayer*)_imp->_parent)->spouse_id;	
}

bool PlayerTaskInterface::CastSkill(int skill_id, int skill_level)
{
	return _imp->CastRune(skill_id,skill_level);	
}

unsigned int PlayerTaskInterface::GetInvEmptySlot()
{
	return _imp->GetInventory().GetEmptySlotCount();
}

void PlayerTaskInterface::LockInventory(bool is_lock)
{
	return _imp->LockInventory(is_lock);
}

unsigned char PlayerTaskInterface::GetShapeMask()
{
	return ((gplayer*)_imp->_parent)->shape_form;
}

bool PlayerTaskInterface::IsAtCrossServer()
{
	return _imp->InCentralServer();
}

bool PlayerTaskInterface::IsKing()
{
	return ((gplayer*)_imp->_parent)->IsKing();
}

int PlayerTaskInterface::GetFactionContrib()
{
	return _imp->GetFactionCumulateContrib();
}

void PlayerTaskInterface::DeliverFactionContrib(int iConsumeContrib,int iExpContrib)
{
	if(_imp->OI_IsMafiaMember()) _imp->IncFactionContrib(iConsumeContrib,iExpContrib);
}

int PlayerTaskInterface::GetFactionConsumeContrib()
{
	return _imp->GetFactionConsumeContrib();
}

void PlayerTaskInterface::TakeAwayFactionConsumeContrib(int ulNum)
{
	if(_imp->OI_IsMafiaMember()) _imp->DecFactionContrib(ulNum, 0); 
}

int PlayerTaskInterface::GetFactionExpContrib()
{
	return _imp->GetFactionExpContrib();
}

void PlayerTaskInterface::TakeAwayFactionExpContrib(int ulNum)
{
	if(_imp->OI_IsMafiaMember()) _imp->DecFactionContrib(0, ulNum);
}
int PlayerTaskInterface::GetForce()
{
	return _imp->_player_force.GetForce();
}
int PlayerTaskInterface::GetForceContribution()
{
	return _imp->GetForceContribution();
}
int PlayerTaskInterface::GetForceReputation()
{
	return _imp->GetForceReputation();
}
bool PlayerTaskInterface::ChangeForceContribution(int iValue)
{
	if(iValue > 0) return _imp->IncForceContribution(iValue);
	else return _imp->DecForceContribution(-iValue);
}
bool PlayerTaskInterface::ChangeForceReputation(int iValue)
{
	if(iValue > 0) return _imp->IncForceReputation(iValue);
	else return _imp->DecForceReputation(-iValue);
}
int PlayerTaskInterface::GetExp()
{
	return _imp->_basic.exp;
}
int PlayerTaskInterface::GetSP()
{
	return _imp->_basic.skill_point;
}
bool PlayerTaskInterface::ReduceExp(int exp)
{
	if(exp > _imp->_basic.exp) exp = _imp->_basic.exp;
	if(exp <= 0) return false;
	_imp->_basic.exp -= exp;
	_imp->SetRefreshState();
	return true;
}
bool PlayerTaskInterface::ReduceSP(int sp)
{
	if(sp > _imp->_basic.skill_point) sp =_imp->_basic.skill_point;
	if(sp <= 0) return false;
	_imp->_basic.skill_point -= sp;
	_imp->SetRefreshState();
	return true;
}
int PlayerTaskInterface::GetForceActivityLevel()
{
	ForceGlobalDataMan & man = world_manager::GetForceGlobalDataMan();
	if(!man.IsDataReady()) return -1;
	return man.GetActivityLevel(GetForce());
}
void PlayerTaskInterface::AddForceActivity(int activity)
{
	GMSV::SendIncreaseForceActivity(GetForce(), activity);
}

bool PlayerTaskInterface::HaveGotTitle(unsigned int id_designation)
{
	return _imp->GetPlayerTitle().IsExistTitle(TITLE_ID(id_designation));
}

void PlayerTaskInterface::DeliverTitle(unsigned int id_designation, int lPeriod)
{
	_imp->GetPlayerTitle().ObtainTitle(TITLE_ID(id_designation),(int)lPeriod);
}

bool PlayerTaskInterface::CheckRefine(unsigned int level_refine, unsigned int num_equips)
{
	return _imp->CheckEquipRefineLevel((int)num_equips,(int)level_refine);
}

void PlayerTaskInterface::PutHistoryGlobalValue(int lKey, int lValue)
{
	world_manager::GetUniqueDataMan().ModifyData(lKey,lValue,true);
}

void PlayerTaskInterface::ModifyHistoryGlobalValue(int lKey, int lValue)
{
	world_manager::GetUniqueDataMan().ModifyData(lKey,lValue,false);
}

int PlayerTaskInterface::GetCurHistoryStageIndex() // ��ǰ��ʷ�׶ε����
{
	return world_manager::GetHistoryMan().GetStageVersion();
}

unsigned int PlayerTaskInterface::GetMaxHistoryLevel()
{
	return _imp->GetHistoricalMaxLevel();
}

unsigned int PlayerTaskInterface::GetReincarnationCount()
{
	return _imp->GetReincarnationTimes();
}

unsigned int PlayerTaskInterface::GetRealmLevel()
{
	return _imp->GetRealmLevel();
}

bool PlayerTaskInterface::IsRealmExpFull()
{
	return _imp->IsRealmExpFull();
}

void PlayerTaskInterface::DeliverRealmExp(unsigned int exp)
{
	_imp->ReceiveRealmExp(exp,0);
}

void PlayerTaskInterface::ExpandRealmLevelMax()
{
	_imp->ExpandRealmLevelMax();
}

unsigned int PlayerTaskInterface::GetObtainedGeneralCardCount()
{
	return _imp->_generalcard_collection.count();
}

void PlayerTaskInterface::AddLeaderShip(unsigned int leader_ship)
{
	return _imp->IncLeadership(leader_ship);
}

unsigned int PlayerTaskInterface::GetObtainedGeneralCardCountByRank(int rank)
{
	return _imp->GetObtainedGeneralCardCountByRank(rank);
}

bool PlayerTaskInterface::CheckTaskForbid(unsigned int task_id)
{
	return world_manager::GetGlobalController().CheckServerForbid(SERVER_FORBID_TASK,(int)task_id);
}
int PlayerTaskInterface::GetWorldContribution()
{
	int contrib = 0, contrib_cost = 0;
	_imp->GetDBWorldContrib(contrib, contrib_cost);
	return contrib;
}
void PlayerTaskInterface::DeliverWorldContribution(int contribution)
{
	_imp->IncWorldContrib(contribution);
}
void PlayerTaskInterface::TakeAwayWorldContribution(int contribution)
{
	_imp->IncWorldContrib(-contribution);
}
int PlayerTaskInterface::GetWorldContributionSpend()
{
	int contrib = 0, contrib_cost = 0;
	_imp->GetDBWorldContrib(contrib, contrib_cost);
	return contrib_cost;
}
bool PlayerTaskInterface::PlayerCanSpendContributionAsWill()
{
	return _imp->CheckPlayerAutoSupport();
}
/*void PlayerTaskInterface::SoloChallengeTaskAward()
{
	_imp->SoloChallengeTaskAward();
}*/
/*----------------------------------��������ӿ�---------------------------------*/

bool PublicQuestInterface::InitAddQuest(int task_id, int child_task_id, int * common_value, int size)
{
	return world_manager::GetPublicQuestMan().InitAddQuest(task_id,child_task_id,common_value,size);	
}

bool PublicQuestInterface::QuestSetStart(int task_id, int * common_value, int size, bool no_change_rank)
{
	return world_manager::GetPublicQuestMan().QuestSetStart(task_id,common_value,size,no_change_rank);	
}

bool PublicQuestInterface::QuestSetFinish(int task_id)
{
	return world_manager::GetPublicQuestMan().QuestSetFinish(task_id);	
}

bool PublicQuestInterface::QuestSetNextChildTask(int task_id, int child_task_id, int * common_value, int size, bool no_change_rank)
{
	return world_manager::GetPublicQuestMan().QuestSetNextChildTask(task_id,child_task_id,common_value,size,no_change_rank);	
}

bool PublicQuestInterface::QuestSetRandContrib(int task_id, int fixed_contrib, int max_rand_contrib, int lowest_contrib)
{
	return world_manager::GetPublicQuestMan().QuestSetRandContrib(task_id,fixed_contrib,max_rand_contrib,lowest_contrib);	
}

int PublicQuestInterface::GetCurSubTask(int task_id)
{
	return world_manager::GetPublicQuestMan().GetCurSubTask(task_id);	
}

int PublicQuestInterface::GetCurTaskStamp(int task_id)
{
	return world_manager::GetPublicQuestMan().GetCurTaskStamp(task_id);	
}

int PublicQuestInterface::GetCurContrib(int task_id, int role_id)
{
	return world_manager::GetPublicQuestMan().GetCurContrib(task_id, role_id);	
}

int PublicQuestInterface::GetCurAllPlace(int task_id, int role_id)
{
	return world_manager::GetPublicQuestMan().GetCurAllPlace(task_id, role_id);	
}

int PublicQuestInterface::GetCurClsPlace(int task_id, int role_id)
{
	return world_manager::GetPublicQuestMan().GetCurClsPlace(task_id, role_id);	
}

bool PublicQuestInterface::QuestAddPlayer(int task_id, int role_id)
{
	return world_manager::GetPublicQuestMan().QuestAddPlayer(task_id,role_id);	
}

bool PublicQuestInterface::QuestRemovePlayer(int task_id, int role_id)
{
	return world_manager::GetPublicQuestMan().QuestRemovePlayer(task_id,role_id);	
}

bool PublicQuestInterface::QuestUpdatePlayerContrib(int task_id, int roleid, int inc_contrib)
{
	return world_manager::GetPublicQuestMan().QuestUpdatePlayerContrib(task_id,roleid,inc_contrib);	
}

void PublicQuestInterface::QuestEnterWorldInit(int task_id, int role_id)
{
	world_manager::GetPublicQuestMan().QuestEnterWorldInit(task_id,role_id);	
}

void PublicQuestInterface::QuestLeaveWorld(int task_id, int role_id)
{
	world_manager::GetPublicQuestMan().QuestLeaveWorld(task_id,role_id);	
}

int PublicQuestInterface::QuestGetGlobalValue(int lKey)
{
	//��������ֻ��BigWorld�д���,����ʹ��world 0�е�ȫ�ֱ���
	return world_manager::GetInstance()->GetWorldByIndex(0)->GetCommonValue(lKey);	
}

void PublicQuestInterface::SetMonsterController(int id, bool bTrigger)
{
	//��������ֻ��BigWorld�д���,����ʹ��world 0
	if(id <= 0) return;
	world * pPlane = world_manager::GetInstance()->GetWorldByIndex(0);
	
	//��������ʵ��
	if(bTrigger)
	{
		pPlane->TriggerSpawn(id);
	}
	else
	{
		pPlane->ClearSpawn(id);
	}
}

void PlayerTaskInterface::LoadFixedEntry(const void* pActiveListBuf, const unsigned int iActiveListLen)
{
	if ( pActiveListBuf && iActiveListLen ) 
	{
		ActiveTaskList* task = (ActiveTaskList*)GetActiveTaskList();
		if (task)
		{
			task->LoadFixedEntry(pActiveListBuf, iActiveListLen);
		}
	}
}

void PlayerTaskInterface::SaveFixedEntry(void* pActiveListBuf, unsigned int & iActiveListLen)
{
	if ( pActiveListBuf ) 
	{
		ActiveTaskList* task = (ActiveTaskList*)GetActiveTaskList();
		if (task)
		{
			task->SaveFixedEntry(pActiveListBuf, iActiveListLen);
		}
	}
}


