#pragma once
#include <cstdint>

class gplayer_imp;
class base_wrapper;

class player_kid {
public:
enum
    {
        KID_SYSTEM_CONFIG_ID = 6858,	
        KID_QUALITY_CONFIG_ID = 6874,
    };
    player_kid(gplayer_imp* p) : _owner(p) {
        _select = -1;
        _lock_data_map = 0;
        _max_data = 0;
    
        memset(&_kid_data, 0, sizeof(_kid_data));
        memset(&_kid_ess, 0, sizeof(_kid_ess));
        _update_time = 0;
        memset(&_addon_mask, 0, sizeof(_addon_mask));
        memset(&_tmp_data, 0, sizeof(_tmp_data));
    }

    // Main logic
    bool CreateKid(const void* buf);
    bool RePool();
    void ClientSync(int type);

    // Core functions
    void Save(base_wrapper& ar);
    void Load(base_wrapper& ar);
    void Swap(player_kid& other);
    const void* SaveToDB();
    void InitFromDB(const void* data, unsigned int size);
    void Heartbeat(int now);
    void StartDay();
    bool OnCreateKid();
    bool UpPool();

    // Course management
    void BuyCourse(int id);
    void SellCourse(int id);
    void MoveCourse(int from, int to);
    void UpCourse(int kidId, int courseId, int level);
    void EndTeach();

    // Kid ops
    void KidModify(int, const void*, unsigned int);
    void KidDeubug(int index);
    void UpdateKid(int index);
    void UpKidLvl(unsigned int id, int level);

    // Debris
    void UseDebri(unsigned int itemId, int amount, unsigned int kidId);
    void AddDebri(unsigned int itemId, unsigned int& outResult);

    // Activation
    void ActivateReward(unsigned int id, unsigned int kidId);
    void ActivateAllAddon();
    void Activate(int id);
    void Deactivate(int id);
    void ActivateTransform();
    void DeactivateTransform();

    static unsigned int NEXT_DAY_TIME();

    // Structs
    struct course_ess {
        int _tid;
        char _lvl;
    } __attribute__((packed, aligned(1)));

    struct kid_data {
        int _course_new[5];            // 0x00
        char _pool_lvl;                // 0x14
        char _free_times;              // 0x15
        int _pool_exp;                 // 0x16
        course_ess _course_inv[14];    // 0x1A
        char _name[16];                // 0x60
        char _gender;                  // 0x70
        int _exp;                      // 0x71
        int _growth_days;              // 0x75
        char _new_day;                 // 0x79
        int _energy;                   // 0x7A
        char _type;                    // 0x7E
        char _status;                  // 0x7F
    };

    struct kid_ess {
        int _lvl;
        int _rahk_lvl;
        int _debris_exp;
        int _tid;
        int _physic_damage;
        int _magic_damage;
        int _defence;
        int _magic_defences[5];
        int _HP;
        int _crit;
    };

    struct tmp_data {
        int shape;
        int attack_type;
        int hp;
        int damage_low;
        int damage_high;
        int damage_magic_low;
        int damage_magic_high;
        int defence;
        int resistance[5];
        int crit_hit;
        int attack_speed;
        float attack_range;
        float speed;
        int attack_degree;
        int defend_degree;
        int phy_inherit;
        int mag_inherit;
        int time_reduce;
        int skill_count;
        int skill[32];
    };
private:
    gplayer_imp* const _owner;     // 0x00
    kid_data _kid_data;            // 0x04
    int _select;                   // 0x84
    kid_ess _kid_ess[6];           // 0x88
    int _update_time;              // 0x1D8
    int64_t _addon_mask[6];        // 0x1DC
    int _max_data;                 // 0x20C
    int _lock_data_map;            // 0x210
    tmp_data _tmp_data;            // 0x214

    // Byte flag accessors
    bool& _flag_18h() { return *reinterpret_cast<bool*>((char*)this + 0x18); }
    bool& _flag_19h() { return *reinterpret_cast<bool*>((char*)this + 0x19); }
    bool& _flag_74h() { return *reinterpret_cast<bool*>((char*)this + 0x74); }
    bool& _flag_83h() { return *reinterpret_cast<bool*>((char*)this + 0x83); }
};