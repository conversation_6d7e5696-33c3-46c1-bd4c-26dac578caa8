 /*
 * FILE: NPCChaseOnAirAgent.cpp
 *
 * DESCRIPTION:   A  class derived from the class CNPCChaseAgent 
 *							which realizes On-Air NPCs' chasing movement.
 *							For now, we don't realize the avoidance of any obstructs!
 *
 * CREATED BY: <PERSON>, 2005/5/12
 *
 * HISTORY: 
 *
 * Copyright (c) 2004 Archosaur Studio, All Rights Reserved.
 */

#include "NPCChaseOnAirAgent.h"


CNPCChaseOnAirStraightAgent::CNPCChaseOnAirStraightAgent(CMap * pMap):CNPCChaseAgent(pMap)
{

}

CNPCChaseOnAirStraightAgent::~CNPCChaseOnAirStraightAgent()
{

}
