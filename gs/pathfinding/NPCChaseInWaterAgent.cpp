 /*
 * FILE: NPCChaseInWaterAgent.cpp
 *
 * DESCRIPTION:   A  class derived from the class CNPCChaseAgent 
 *							which realizes In-Water NPCs' chasing movement.
 *							For now, we don't realize the avoidance of any obstructs!
 *
 * CREATED BY: <PERSON>, 2005/5/4
 *
 * HISTORY: 
 *
 * Copyright (c) 2004 Archosaur Studio, All Rights Reserved.
 */

#include "NPCChaseInWaterAgent.h"


CNPCChaseInWaterStraightAgent::CNPCChaseInWaterStraightAgent(CMap * pMap):CNPCChaseAgent(pMap)
{

}

CNPCChaseInWaterStraightAgent::~CNPCChaseInWaterStraightAgent()
{

}
