{"files.associations": {"atomic": "cpp", "deque": "cpp", "ios": "cpp", "list": "cpp", "system_error": "cpp", "vector": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xstring": "cpp", "xtree": "cpp", "thread": "cpp", "xlocale": "cpp", "xutility": "cpp", "compare": "cpp", "functional": "cpp", "memory": "cpp", "ratio": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "xmemory": "cpp", "xtr1common": "cpp", "algorithm": "cpp", "bitset": "cpp", "iterator": "cpp", "bit": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "complex": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "map": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "queue": "cpp", "set": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "string": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "xfacet": "cpp", "xlocinfo": "cpp", "xlocnum": "cpp", "xstddef": "cpp", "xlocmon": "cpp", "forward_list": "cpp", "unordered_set": "cpp", "any": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cwctype": "cpp", "array": "cpp", "cfenv": "cpp", "charconv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "cuchar": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "regex": "cpp", "string_view": "cpp", "future": "cpp", "iomanip": "cpp", "numbers": "cpp", "ranges": "cpp", "scoped_allocator": "cpp", "shared_mutex": "cpp", "span": "cpp", "typeindex": "cpp", "valarray": "cpp", "variant": "cpp"}}