// https://github.com/vinniefalco/LuaBridge
// Copyright 2021, <PERSON><PERSON>
// Copyright 2012, <PERSON> <<EMAIL>>
// SPDX-License-Identifier: MIT

#pragma once

#include "Config.h"
#include "Stack.h"

namespace luabridge::detail {

//=================================================================================================
/**
 * @brief Scope guard.
 */
template <class F>
class ScopeGuard
{
public:
    template <class V>
    ScopeGuard(V&& v)
        : m_func(std::forward<V>(v))
        , m_shouldRun(true)
    {
    }

    ~ScopeGuard()
    {
        if (m_shouldRun)
            m_func();
    }

    void reset()
    {
        m_shouldRun = false;
    }

private:
    F m_func;
    bool m_shouldRun;
};

template <class F>
ScopeGuard(F&&) -> ScopeGuard<F>;

} // namespace luabridge::detail
