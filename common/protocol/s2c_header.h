#ifndef __S2C_HEADER_H__
#define __S2C_HEADER_H__

#include "../types.h"

#pragma pack(1)

namespace S2C
{
	enum
	{
		DROP_TYPE_GM,
		DROP_TYPE_PLAYER,
		DROP_TYPE_TAKEOUT,
		DROP_TYPE_TASK,
		DROP_TYPE_RECHARGE,
		DROP_TYPE_DESTROY,
		DROP_TYPE_DEATH,
		DROP_TYPE_PRODUCE,
		DROP_TYPE_DECOMPOSE,
		DROP_TYPE_TRADEAWAY,
		DROP_TYPE_RESURRECT,
		DROP_TYPE_USE,
		DROP_TYPE_RUNE,
		DROP_TYPE_EXPIRE,

		DROP_TYPE_FASHION_CODEX, /*170+ Códice*/
		DROP_TYPE_FLYSWORD_CODEX, /*170+ Códice*/

	};

	enum		//elf cmd lgc
	{
		ELF_LEVELUP,
		E<PERSON>_LEARN_SKILL,
		ELF_FORGET_SKILL,
		E<PERSON>_REFINE,
		E<PERSON>_DECOMPOSE,
		E<PERSON>_DEC_ATTRIBUTE,
		E<PERSON>_ADD_GENIUS,
		E<PERSON>_EQUIP_ITEM,
		E<PERSON>_DESTROY_ITEM,
		ELF_RECHARGE,
	};
	
	enum		//CMD
	{
		PLAYER_INFO_1,
		PLAYER_INFO_2,
		PLAYER_INFO_3,
		PLAYER_INFO_4,
		PLAYER_INFO_1_LIST,

		PLAYER_INFO_2_LIST,			//5
		PLAYER_INFO_3_LIST,
		PLAYER_INFO_23_LIST,
		SELF_INFO_1,
		NPC_INFO_LIST,

		MATTER_INFO_LIST,			//10
		NPC_ENTER_SLICE,
		PLAYER_ENTER_SLICE,
		OBJECT_LEAVE_SLICE,
		OBJECT_NOTIFY_POS,		//这个命令时暂时的？
		
		OBJECT_MOVE,				//15
		NPC_ENTER_WORLD,
		PLAYER_ENTER_WORLD,
		MATTER_ENTER_WORLD,
		PLAYER_LEAVE_WORLD,
		
		NPC_DEAD,				//20
		OBJECT_DISAPPEAR,
		OBJECT_START_ATTACK,			//已经作废 ，完全没有用 
		SELF_STOP_ATTACK,			//
		SELF_ATTACK_RESULT,
		
		ERROR_MESSAGE,				//25	
		BE_ATTACKED,
		PLAYER_DEAD,
		BE_KILLED,
		PLAYER_REVIVAL,
		
		PICKUP_MONEY,				//30
		PICKUP_ITEM,
		PLAYER_INFO_00,
		NPC_INFO_00,
		OUT_OF_SIGHT_LIST,
		
		OBJECT_STOP_MOVE,			//35
		RECEIVE_EXP,
		LEVEL_UP,
		SELF_INFO_00,
		UNSELECT,

		SELF_ITEM_INFO,				//40
		SELF_ITEM_EMPTY_INFO,
		SELF_INVENTORY_DATA,
		SELF_INVENTORY_DETAIL_DATA,
		EXCHANGE_INVENTORY_ITEM,

		MOVE_INVENTORY_ITEM,			//45
		PLAYER_DROP_ITEM,
		EXCHANGE_EQUIPMENT_ITEM,
		EQUIP_ITEM,
		MOVE_EQUIPMENT_ITEM,	

		SELF_GET_EXT_PROPERTY,			//50
		SET_STATUS_POINT,
		SELECT_TARGET,
		PLAYER_EXTPROP_BASE,
		PLAYER_EXTPROP_MOVE,

		PLAYER_EXTPROP_ATTACK,			//55
		PLAYER_EXTPROP_DEFENSE,
		TEAM_LEADER_INVITE,
		TEAM_REJECT_INVITE,
		TEAM_JOIN_TEAM,

		TEAM_MEMBER_LEAVE,			//60
		TEAM_LEAVE_PARTY,
		TEAM_NEW_MEMBER,
		TEAM_LEADER_CANCEL_PARTY,
		TEAM_MEMBER_DATA,
							
		TEAMMATE_POS,				//65
		EQUIPMENT_DATA,			//用户的装备数据，影响表现
		EQUIPMENT_INFO_CHANGED,	
		EQUIPMENT_DAMAGED,		//装备损坏
		TEAM_MEMBER_PICKUP,		//队友捡起装备

		NPC_GREETING,				//70
		NPC_SERVICE_CONTENT,
		PURCHASE_ITEM,
		ITEM_TO_MONEY,	
		REPAIR_ALL,

		REPAIR,					//75
		RENEW,
		SPEND_MONEY,
		PICKUP_MONEY_IN_TRADE,
		PICKUP_ITEM_IN_TRADE,

		PICKUP_MONEY_AFTER_TRADE,		//80
		PICKUP_ITEM_AFTER_TRADE,
		GET_OWN_MONEY,
		OBJECT_ATTACK_ONCE,			//已经作废 ，完全没有用
		SELF_START_ATTACK,
	
		OBJECT_CAST_SKILL,			//85
		SKILL_INTERRUPTED,
		SELF_SKILL_INTERRUPTED,
		SKILL_PERFORM,
		OBJECT_BE_ATTACKED,			//已经作废 ，完全没有用

		SKILL_DATA,				//90
		PLAYER_USE_ITEM,
		EMBED_ITEM,
		CLEAR_EMBEDDED_CHIP,
		COST_SKILL_POINT,

		LEARN_SKILL,				//95
		OBJECT_TAKEOFF,
		OBJECT_LANDING,
		FLYSWORD_TIME_CAPACITY,
		OBTAIN_ITEM,

		PRODUCE_START,				//100
		PRODUCE_ONCE,
		PRODUCE_END,
		DECOMPOSE_START,
		DECOMPOSE_END,

		TASK_DATA,				//105
		TASK_VAR_DATA,
		OBJECT_START_USE,
		OBJECT_CANCEL_USE,
		OBJECT_USE_ITEM,

		OBJECT_START_USE_WITH_TARGET,		//110
		OBJECT_SIT_DOWN,
		OBJECT_STAND_UP,
		OBJECT_DO_EMOTE,
		SERVER_TIMESTAMP,

		NOTIFY_ROOT,				//115
		DISPEL_ROOT,
		INVADER_RISE,
		PARIAH_RISE,
		INVADER_FADE,

		OBJECT_ATTACK_RESULT,			//120
		BE_HURT,
		HURT_RESULT,
		SELF_STOP_SKILL,
		UPDATE_VISIBLE_STATE,

		OBJECT_STATE_NOTIFY,			//125
		PLAYER_GATHER_START,
		PLAYER_GATHER_STOP,
		TRASHBOX_PASSWD_CHANGED,
		TRASHBOX_PASSWD_STATE,

		TRASHBOX_OPEN,				//130
		TRASHBOX_CLOSE,	
		TRASHBOX_WEALTH,
		EXCHANGE_TRASHBOX_ITEM,
		MOVE_TRASHBOX_ITEM,
		
		EXCHANGE_TRASHBOX_INVENTORY,		//135
		INVENTORY_ITEM_TO_TRASH,
		TRASH_ITEM_TO_INVENTORY,
		EXCHANGE_TRASH_MONEY,
		ENCHANT_RESULT,

		SELF_NOTIFY_ROOT,			//140
		OBJECT_DO_ACTION,
		SELF_SKILL_ATTACK_RESULT,
		OBJECT_SKILL_ATTACK_RESULT,
		BE_SKILL_ATTACKED,

		PLAYER_SET_ADV_DATA,			//145
		PLAYER_CLR_ADV_DATA,
		PLAYER_IN_TEAM,		
		TEAM_APPLY_REQUEST,
		OBJECT_DO_EMOTE_RESTORE,

		CONCURRENT_EMOTE_REQUEST,		//150
		DO_CONCURRENT_EMOTE,			
		MATTER_PICKUP,
		MAFIA_INFO_NOTIFY,
		MAFIA_TRADE_START,
		
		MAFIA_TRADE_END,			//155
		TASK_DELIVER_ITEM,
		TASK_DELIVER_REPUTATION,
		TASK_DELIVER_EXP,
		TASK_DELIVER_MONEY,

		TASK_DELIVER_LEVEL2,			//160
		PLAYER_REPUTATION,
		IDENTIFY_RESULT,
		PLAYER_CHANGE_SHAPE,
		OBJECT_ENTER_SANCTUARY,

		OBJECT_LEAVE_SANCTUARY,			//165
		PLAYER_OPEN_MARKET,
		SELF_OPEN_MARKET,
		PLAYER_CANCEL_MARKET,
		PLAYER_MARKET_INFO,

		PLAYER_MARKET_TRADE_SUCCESS,		//170
		PLAYER_MARKET_NAME,
		PLAYER_START_TRAVEL,
		SELF_START_TRAVEL,
		PLAYER_COMPLETE_TRAVEL,

		GM_TOGGLE_INVINCIBLE,			//175
		GM_TOGGLE_INVISIBLE,
		SELF_TRACE_CUR_POS,
		OBJECT_CAST_INSTANT_SKILL,	
		ACTIVATE_WAYPOINT,

		PLAYER_WAYPOINT_LIST,			//180
		UNLOCK_INVENTORY_SLOT,
		TEAM_INVITE_PLAYER_TIMEOUT,
		PLAYER_ENABLE_PVP,
		PLAYER_DISABLE_PVP,

		PLAYER_PVP_COOLDOWN,			//185
		COOLDOWN_DATA,
		SKILL_ABILITY_NOTFIY,
		PERSONAL_MARKET_AVAILABLE,
		BREATH_DATA,

		PLAYER_STOP_DIVE,			//190
		TRADE_AWAY_ITEM,
		PLAYER_ENABLE_FASHION_MODE,
		ENABLE_FREE_PVP_MODE,
		OBJECT_IS_INVALID,

		PLAYER_ENABLE_EFFECT,			//195
		PLAYER_DISABLE_EFFECT,
		ENABLE_RESURRECT_STATE,		
		SET_COOLDOWN,
		CHANGE_TEAM_LEADER,		

		KICKOUT_INSTANCE,			//200
		PLAYER_COSMETIC_BEGIN,
		PLAYER_COSMETIC_END,
		COSMETIC_SUCCESS,
		OBJECT_CAST_POS_SKILL,

		CHANGE_MOVE_SEQ,			//205
		SERVER_CONFIG_DATA,
		PLAYER_RUSH_MODE,
		TRASHBOX_CAPACITY_NOTIFY,
		NPC_DEAD_2,

		PRODUCE_NULL,				//210
		ACTIVE_PVP_COMBAT_STATE,
		DOUBLE_EXP_TIME,
		AVAILABLE_DOUBLE_EXP_TIME,
		DUEL_RECV_REQUEST,

		DUEL_REJECT_REQUEST,			//215
		DUEL_PREPARE,
		DUEL_CANCEL,
		DUEL_START,
		DUEL_STOP,
		
		DUEL_RESULT,				//220
		PLAYER_BIND_REQUEST,
		PLAYER_BIND_INVITE,
		PLAYER_BIND_REQUEST_REPLY,
		PLAYER_BIND_INVITE_REPLY,

		PLAYER_BIND_START,			//225
		PLAYER_BIND_STOP,
		PLAYER_MOUNTING,
		PLAYER_EQUIP_DETAIL,
		ELSE_DUEL_START,

		PARIAH_DURATION,			//230
		PLAYER_GAIN_PET,
		PLAYER_FREE_PET,
		PLAYER_SUMMON_PET,
		PLAYER_RECALL_PET,

		PLAYER_START_PET_OP,			//235
		PLAYER_STOP_PET_OP,
		PLAYER_PET_RECV_EXP,
		PLAYER_PET_LEVELUP,
		PLAYER_PET_ROOM,

		PLAYER_PET_ROOM_CAPACITY,		//240
		PLAYER_PET_HONOR_POINT,
		PLAYER_PET_HUNGER_GAUGE,
		ENTER_BATTLEGROUND,
		TURRET_LEADER_NOTIFY,

		BATTLE_RESULT,				//245
		BATTLE_SCORE,
		PET_DEAD,
		PET_REVIVE,
		PET_HP_NOTIFY,

		PET_AI_STATE,				//250
		REFINE_RESULT,
		PET_SET_COOLDOWN,
		PLAYER_CASH,
		PLAYER_BIND_SUCCESS,

		PLAYER_CHANGE_INVENTORY_SIZE,		//255
		PLAYER_PVP_MODE,
		PLAYER_WALLOW_INFO,
		PLAYER_USE_ITEM_WITH_ARG,
		OBJECT_USE_ITEM_WITH_ARG,

		PLAYER_CHANGE_SPOUSE,			//260
		NOTIFY_SAFE_LOCK,
		ELF_VIGOR,		//lgc
		ELF_ENHANCE,
		ELF_STAMINA,
		
		ELF_CMD_RESULT,		//265
		COMMON_DATA_NOTIFY,
		COMMON_DATA_LIST,
		PLAYER_ELF_REFINE_ACTIVATE,
		PLAYER_CAST_ELF_SKILL,

		MALL_ITEM_PRICE,		//270
		MALL_ITEM_BUY_FAILED,
		PLAYER_ELF_LEVELUP,
		PLAYER_PROPERTY,
		PLAYER_CAST_RUNE_SKILL,

		PLAYER_CAST_RUNE_INSTANT_SKILL,	//275
		EQUIP_TRASHBOX_ITEM,
		SECURITY_PASSWD_CHECKED,
		OBJECT_INVISIBLE,
		HP_STEAL,
		
		PLAYER_DIVIDEND,                //280
		DIVIDEND_MALL_ITEM_PRICE,
		DIVIDEND_MALL_ITEM_BUY_FAILED,
		ELF_EXP,
		PUBLIC_QUEST_INFO,

		PUBLIC_QUEST_RANKS,				//285
		MULTI_EXP_INFO,
		CHANGE_MULTI_EXP_STATE,
		WORLD_LIFE_TIME,
		WEDDING_BOOK_LIST,
		
		WEDDING_BOOK_SUCCESS,		//290
		CALC_NETWORK_DELAY,
		PLAYER_KNOCKBACK,
		PLAYER_SUMMON_PLANT_PET,
		PLANT_PET_DISAPPEAR,

		PLANT_PET_HP_NOTIFY,		//295
		PET_PROPERTY,
		FACTION_CONTRIB_NOTIFY,
		FACTION_FORTRESS_INFO,
		ENTER_FACTIONFORTRESS,

		FACTION_RELATION_NOTIFY,	//300
		PLAYER_EQUIP_DISABLED,
		PLAYER_SPEC_ITEM_LIST,
		OBJECT_START_PLAY_ACTION,
		OBJECT_STOP_PLAY_ACTION,

		CONGREGATE_REQUEST,			//305
		REJECT_CONGREGATE,
		CONGREGATE_START,
		CANCEL_CONGREGATE,
		ENGRAVE_START,

		ENGRAVE_END,				//310
		ENGRAVE_RESULT,
		DPS_DPH_RANK,
		ADDONREGEN_START,
		ADDONREGEN_END,
		
		ADDONREGEN_RESULT,			//315
		INVISIBLE_OBJ_LIST,
		SET_PLAYER_LIMIT,
		PLAYER_TELEPORT,
		OBJECT_FORBID_BE_SELECTED,

		PLAYER_INVENTORY_DETAIL,	//320
		PLAYER_FORCE_DATA,
		PLAYER_FORCE_CHANGED,
		PLAYER_FORCE_DATA_UPDATE,
		FORCE_GLOBAL_DATA,

		ADD_MULTIOBJ_EFFECT,		//325
		REMOVE_MULTIOBJ_EFFECT,
		ENTER_WEDDING_SCENE,
		PRODUCE4_ITEM_INFO,
		ONLINE_AWARD_DATA,

		TOGGLE_ONLINE_AWARD,		//330
		PLAYER_PROFIT_TIME,
		ENTER_NONPENALTY_PVP_STATE,
		SELF_COUNTRY_NOTIFY,
		PLAYER_COUNTRY_CHANGED,

		ENTER_COUNTRYBATTLE,		//335
		COUNTRYBATTLE_RESULT,
		COUNTRYBATTLE_SCORE,
		COUNTRYBATTLE_RESURRECT_REST_TIMES,
		COUNTRYBATTLE_FLAG_CARRIER_NOTIFY,
		
		COUNTRYBATTLE_BECAME_FLAG_CARRIER,	//340
		COUNTRYBATTLE_PERSONAL_SCORE,
		COUNTRYBATTLE_FLAG_MSG_NOTIFY,
		DEFENSE_RUNE_ENABLED,
		COUNTRYBATTLE_INFO,
		
		SET_PROFITTIME_STATE,		//345
		CASH_MONEY_EXCHG_RATE,
		PET_REBUILD_INHERIT_START,
		PET_REBUILD_INHERIT_INFO,
		PET_REBUILD_INHERIT_END,
		
		PET_EVOLUTION_DONE,			//350
		PET_REBUILD_NATURE_START,
		PET_REBUILD_NATURE_INFO,
		PET_REBUILD_NATURE_END,
		EQUIP_ADDON_UPDATE_NOTIFY,

		SELF_KING_NOTIFY,			//355
		PLAYER_KING_CHANGED,
		NOTIFY_MERIDIAN_DATA,	
		TRY_REFINE_MERIDIAN_RESULT,
		COUNTRYBATTLE_STRONGHOLD_STATE_NOTIFY,
	
		QUERY_TOUCH_POINT,		//360
		COST_TOUCH_POINT,
		QUERY_ADDUP_MONEY,
		QUERY_TITLE,
		CHANGE_CURR_TITLE,	

		MODIFY_TITLE_NOTIFY, 		//365
		REFRESH_SIGNIN,
		PARALLEL_WORLD_INFO,
		PLAYER_REINCARNATION,
		REINCARNATION_TOME_INFO,

		ACTIVATE_REINCARNATION_TOME,	//370
		UNIQUE_DATA_NOTIFY,
		GIFTCARD_REDEEM_NOTIFY,
		REALM_EXP,
		REALM_LEVEL_UP,     
		
		ENTER_TRICKBATTLE,				//375
		TRICKBATTLE_PERSONAL_SCORE,		
		TRICKBATTLE_CHARIOT_INFO,	
		PLAYER_LEADERSHIP,
		GENERALCARD_COLLECTION_DATA,
		
		ADD_GENERALCARD_COLLECTION,		//380
		REFRESH_FATERING,
		MINE_GATHERD,
		PLAYER_ACTIVE_COMBAT,
		PLAYER_QUERY_CHARIOTS,

		COUNTRYBATTLE_LIVE_RESULT,		//385
		RANDOM_MALL_SHOPPING_RESULT,
		PLAYER_MAFIA_PVP_MASK_NOTIFY,
		PLAYER_WORLD_CONTRIB,
		RANDOM_MAP_ORDER,

		SCENE_SERVICE_NPC_LIST,			//390
		NPC_VISIBLE_TID_NOTIFY,
		CLIENT_SCREEN_EFFECT,
        EQUIP_CAN_INHERIT_ADDONS,
		COMBO_SKILL_PREPARE,

		INSTANCE_REENTER_NOTIFY,		//395
		PRAY_DISTANCE_CHANGE,
		ASTROLABE_INFO_NOTIFY,
		ASTROLABE_OPERATE_RESULT,
		SOLO_CHALLENGE_AWARD_INFO_NOTIFY,
		
		SOLO_CHALLENGE_OPERATE_RESULT,  //400
		SOLO_CHALLENGE_CHALLENGING_STATE_NOTIFY,
		SOLO_CHALLENGE_BUFF_INFO_NOTIFY,
        PROPERTY_SCORE_RESULT,
		MNFACTION_RESOURCE_POINT_INFO,
		
		MNFACTION_PLAYER_COUNT_INFO,  //405
		MNFACTION_RESULT,               
		MNFACTION_RESOURCE_TOWER_STATE_INFO,
		MNFACTION_SWITCH_TOWER_STATE_INFO,
		MNFACTION_TRANSMIT_POS_STATE_INFO,
		
		MNFACTION_RESOURCE_POINT_STATE_INFO, //410
		MNFACTION_PLAYER_FACTION_INFO,
		MNFACTION_BATTLE_GROUND_HAVE_START_TIME,
		MNFACTION_FACTION_KILLED_PLAYER_NUM,
		MNFACTION_SHOUT_AT_THE_CLIENT,

		MNFACTION_PLAYER_POS_INFO,          //415
		FIX_POSITION_TRANSMIT_ADD_POSITION,
		FIX_POSITION_TRANSMIT_DELETE_POSITION,
		FIX_POSITION_TRANSMIT_RENAME,
		FIX_POSITION_ENERGY_INFO,

		FIX_POSITION_ALL_INFO,              // 420
		CASH_VIP_MALL_ITEM_PRICE,
		CASH_VIP_MALL_ITEM_BUY_RESULT,
		CASH_VIP_INFO_NOTIFY,
		PURCHASE_LIMIT_INFO_NOTIFY,

        LOOKUP_ENEMY_RESULT,                // 425
        CASH_RESURRECT_INFO,
		HOME_TASK_INFO,						
		BODY_SCALE_NOTIFY,					
		FILTER_SOUL_INFO,					

// -- cgame recv new 155 - 162 -- //
		
		CGAME_RECV_430,						//430
		CGAME_RECV_431,
		CGAME_RECV_432,
		GET_LOTERY_ITEMS,
		GET_TREASURE_INFO,
		
		CGAME_RECV_435,						//435
		PLAYER_WORLD_SPEAK_INFO,
		CGAME_RECV_437,
		PLAYER_KILLED_BY_PLAYER,
		GET_LIB_ITEMS,
		
		CGAME_RECV_440,						//440
		ARENA_TEAM_INVITE,
		ARENA_PLAYER_FACTION_INFO,
		ARENA_BATTLE_GROUP_INFO,
		GLYPH_SLOT_INFO,
		
		GLYPH_DATA,							//445
		ARENA_BATTLE_RESULT,
		ARENA_BATTLE_GROUP_PROP,
		ARENA_BATTLE_OBJECT_STATE_NOTIFY,
		CGAME_RECV_449,
		
		PLAYER_CARRIER_UP,					//450
		PLAYER_CARRIER_SELF_UP,
		PLAYER_CARRIER_DOWN,
		CGAME_RECV_453,
		GLYPH_MANAGER_RE,
		
		GET_CHARGE_MERC,					//455
		CARRIER_SYNC_INFO,
		CGAME_RECV_457,
		CGAME_RECV_458,
		SKILL_GLYPH_INFO,
		
		CARRIER_SKILL_COOLDOWN,						//460
		CARRIER_PROP,
		CGAME_RECV_462,
		CGAME_RECV_463,
		CGAME_RECV_464,
		
		CGAME_RECV_465,						//465
		CGAME_RECV_466,
		CGAME_RECV_467,
		CGAME_RECV_468,
		CGAME_RECV_469,
		
		CGAME_RECV_470,						//470
		CGAME_RECV_471,
		CGAME_RECV_472,
		CGAME_RECV_473,
		CGAME_RECV_474,
		
		CGAME_RECV_475,						//475
		CGAME_RECV_476,
		CGAME_RECV_477,
		CGAME_RECV_478,
		CGAME_RECV_479,
		
		GET_IMPERIAL_SPIRIT,				//480
		CGAME_RECV_481,
		CGAME_RECV_482,
		CGAME_RECV_483,
		CGAME_RECV_484,
		
		CGAME_RECV_485,						//485
		CGAME_RECV_486,
		GET_STORAGE_POINTS,
		GET_FINISH_NOTIFY,
		REFINE_BIBLE_RESULT,
		
		TRANSFER_REFINE_BIBLE,				//490
		CGAME_RECV_491,
		CGAME_RECV_492,
		CGAME_RECV_493,
		CGAME_RECV_494,
		
		CGAME_RECV_495,						//495
		CGAME_RECV_496,
		ENGRAVE_NEW_INFO,
		CGAME_RECV_498,
		CGAME_RECV_499,
		
		CGAME_RECV_500,						//500
		CGAME_RECV_501,
		CGAME_RECV_502,
		GET_ANECDOTE_INFO,
		CGAME_RECV_504,
		
		CGAME_RECV_505,						//505
		SKILL_COOLDOWN_LIMIT,
		PLAYER_REPOSITORY_INFO,
		PLAYER_REPOSITORY_STORAGE,
		CGAME_RECV_509,
		
		ARMOR_INFO_PURIFICATION,						//510
		ARMOR_INFO_NOTIFY, // G17
		GET_WEAPON_VSTATE,
		GET_SHIELD_ENERGY,
		CGAME_RECV_514,
		
		CGAME_RECV_515,						//515
		CGAME_RECV_516,
		CGAME_RECV_517,
		CGAME_RECV_518,
		CGAME_RECV_519,

		CGAME_RECV_520,
		CGAME_RECV_521,
		GET_SERVER_TIME,
		GET_LOTERY_INFO,
		CGAME_RECV_524,
		
		CGAME_RECV_525,
		CGAME_RECV_526,
		CGAME_RECV_527,
		CGAME_RECV_528,
		CGAME_RECV_529,
		
		CELESTIAL_MEMORIAL_INFO,
		CELESTIAL_MEMORIAL_LOTTERY,
		CGAME_RECV_532,
		CGAME_RECV_533,
		CGAME_RECV_534,
		
		CGAME_RECV_535,
		CGAME_RECV_536,
		CGAME_RECV_537,
		CGAME_RECV_538,
		PET_SKILL_DATA,
		
		PET_MASTER_PROP,
		PET_SKIN_INFO,
		PET_SKIN_STORAGE,
		CGAME_RECV_543,
		CGAME_RECV_544,
		
		CODEX_FASHION_DYE,
		CODEX_FASHION_ACTIVATE,
		CODEX_REWARD_FIRST,
		CODEX_FLY_CONSUM_INFO,
		CODEX_IMPROVE_INFO,
		
		CGAME_RECV_550,
		CODEX_FLY_EQUIP_INFO,
		CODEX_PET_CONSUM_INFO,
		CGAME_RECV_553,
		CODEX_RENAME_PET_INFO,
		
		CODEX_REWARD_TITLE,
		CODEX_FASHION_STORAGE,
		CGAME_RECV_557,
		REWARD_INTERFACE_NOTIFY,
		CGAME_RECV_559,
		
		CGAME_RECV_560,
		CGAME_RECV_561,
		CGAME_RECV_562,
		CGAME_RECV_563,
		CGAME_RECV_564,
		
		CGAME_RECV_565,
		CGAME_RECV_566,
		CGAME_RECV_567,
		CGAME_RECV_568,
		CGAME_RECV_569,
		
		CGAME_RECV_570,
		CGAME_RECV_571,
		CGAME_RECV_572,
		CGAME_RECV_573,
		CGAME_RECV_574,
		
		CGAME_RECV_575,
		CGAME_RECV_576,
		CGAME_RECV_577,
		CGAME_RECV_578,
		CGAME_RECV_579,
		
		CGAME_RECV_580,
		CGAME_RECV_581,
		CGAME_RECV_582,
		CGAME_RECV_583,
		CGAME_RECV_584,
		
		CGAME_RECV_585,
		CGAME_RECV_586,
		CGAME_RECV_587,
		DYNAMIC_GENESIS_LEVEL,
		MATERIAL_REFINE_RESULT,
		
		ACTIVITY_EVENT_SPEND_CASH,
		ACTIVITY_EVENT_SHOP,
		CGAME_RECV_592,
		CGAME_RECV_593,
		CGAME_RECV_594,
		
// -- cgame recv new 172+ -- //

		/*176+*/
		OPEN_FASHION_GSHOP_BOX = 604, // size = 178
		PORTATIL_PICTURE_INFO = 605,
		PORTATIL_PICTURE_STORAGE,


		//180+
		
		CGAME_RECV_611, // 15 bytes
		CGAME_RECV_614, // 6 bytes
		BARD_WEAPON_ENERGY = 616,
		CGAME_RECV_617, // 6 bytes
	};
	
	
	enum //main packets  
	{
		GET_MAIN_INFO = 13001,
		GET_MAIN_INFO_OTHER = 13002,
		LIB_PRODUCE_RESULT = 13500,
	};
	
	
};

#pragma pack()
#endif

