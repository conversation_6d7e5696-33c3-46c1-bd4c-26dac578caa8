#ifndef __S2C_ERROR_MSG_H__
#define __S2C_ERROR_MSG_H__

#include "../types.h"

#pragma pack(1)

namespace S2C
{
	enum
	{
		ERR_SUCCESS, // 0
		ERR_INVALID_TARGET,
		ERR_OUT_OF_RANGE,
		ERR_FATAL_ERR,
		ERR_COMMAND_IN_ZOMBIE,
		ERR_ITEM_NOT_IN_INVENTORY, // 5
		ERR_ITEM_CANT_PICKUP,
		ERR_INVENTORY_IS_FULL,
		ERR_ITEM_CANNOT_EQUIP,
		ERR_CANNOT_ATTACK,
		ERR_TEAM_CANNOT_INVITE, // 10
		ERR_TEAM_JOIN_FAILED,
		ERR_TEAM_ALREADY_INVITE,
		ERR_TEAM_INVITE_TIMEOUT,
		ERR_SERVICE_UNAVILABLE,
		ERR_SERVICE_ERR_REQUEST, // 15
		ERR_OUT_OF_FUND,
		ERR_CANNOT_LOGOUT,
		ERR_CANNOT_USE_ITEM,
		ERR_TASK_NOT_AVAILABLE,
		ERR_SKILL_NOT_AVAILABLE, // 20
		ERR_CANNOT_EMBED,
		ERR_CANNOT_LEARN_SKILL,
		ERR_CANNOT_HEAL_IN_COMBAT,
		ERR_CANNOT_RECHARGE,
		ERR_NOT_ENOUGH_MATERIAL, // 25
		ERR_PRODUCE_FAILED,
		ERR_DECOMPOSE_FAILED,
		ERR_CANNOT_SIT_DOWN,
		ERR_CANNOT_EQUIP_NOW,
		ERR_MINE_HAS_BEEN_LOCKED, // 30
		ERR_MINE_HAS_INVALID_TOOL,
		ERR_MINE_GATHER_FAILED,
		ERR_OTHER_SESSION_IN_EXECUTE,
		ERR_INVALID_PASSWD_FORMAT,
		ERR_PASSWD_NOT_MATCH, // 35
		ERR_TRASH_BOX_NOT_OPEN,
		ERR_ENOUGH_MONEY_IN_TRASH_BOX,
		ERR_TEAM_REFUSE_APPLY,
		ERR_CONCURRENT_EMOTE_REFUSED,
		ERR_EQUIPMENT_IS_LOCKED, // 40
		ERR_CANNOT_OPEN_PLAYER_MARKET,
		ERR_INVALID_ITEM,
		ERR_YOU_HAS_BEEN_BANISHED,
		ERR_CAN_NOT_DROP_ITEM,
		ERR_INVALID_PRIVILEGE, // 45
		ERR_PLAYER_NOT_EXIST,
		ERR_CAN_NOT_DISABLE_PVP_STATE,
		ERR_CAN_NOT_UNLEARN_SKILL,
		ERR_COMMAND_IN_IDLE,
		ERR_COMMAND_IN_SEALED, // 50
		ERR_LEVEL_NOT_MATCH,
		ERR_CANNOT_ENTER_INSTANCE,
		ERR_SKILL_IS_COOLING,
		ERR_OBJECT_IS_COOLING,
		ERR_CANNOT_FLY, // 55
		ERR_CAN_NOT_RESET_INSTANCE,
		ERR_INVENTORY_IS_LOCKED,
		ERR_TOO_MANY_PLAYER_IN_INSTANCE,
		ERR_TOO_MANY_INSTANCE,
		ERR_FACTION_BASE_NOT_READY, // 60
		ERR_FACTION_BASE_DENIED,
		ERR_CAN_NOT_JUMP_BETWEEN_INSTANCE,
		ERR_NOT_ENOUGH_REST_TIME,
		ERR_CANNOT_DUEL_TWICE,
		ERR_CREATE_DUEL_FAILED, // 65
		ERR_INVALID_OPERATION_IN_COMBAT,
		ERR_INVALID_GENDER,
		ERR_INVALID_BIND_REQUEST,
		ERR_INVALID_BIND_REPLY,
		ERR_FORBIDDED_OPERATION, // 70
		ERR_PET_IS_ALEARY_ACTIVE,
		ERR_PET_IS_NOT_EXIST,
		ERR_PET_IS_NOT_ACTIVE,
		ERR_PET_FOOD_TYPE_NOT_MATCH,
		ERR_BATTLEFIELD_IS_CLOSED, // 75
		ERR_PET_CAN_NOT_BE_HATCHED,
		ERR_PET_CAN_NOT_BE_RESTORED,
		ERR_FACTION_IS_NOT_MATCH,
		ERR_CANNOT_QUERY_ENEMY_EQUIP,
		ERR_NPC_SERVICE_IS_BUSY, // 80
		ERR_PET_CAN_NOT_MOUNT,
		ERR_CAN_NOT_RESET_PP,
		ERR_BATTLEFIELD_IS_FINISHED,
		ERR_HERE_CAN_NOT_DUEL,
		ERR_SUMMON_PET_INVALID_POS, // 85
		ERR_CONTROL_TOO_MANY_TURRETS,
		ERR_CANNOT_SUMMON_DEAD_PET,
		ERR_PET_IS_NOT_DEAD,
		ERR_CANNOT_BIND_HERE,
		ERR_INVALID_PLAYER_CALSS, // 90
		ERR_RUNE_IS_IN_EFFECT,
		ERR_REFINE_CAN_NOT_REFINE,
		ERR_PET_SKILL_IN_COOLDOWN,
		ERR_GSHOP_INVALID_REQUEST,
		ERR_CAN_NOT_STOP_DESTROY_BIND_ITEM, // 95
		ERR_ITEM_CANNOT_UNEQUIP,
		ERR_USE_ITEM_FAILED,
		ERR_DYE_FAILED,
		ERR_FASHION_CAN_NOT_BE_DYED,
		ERR_DYE_NOT_ENOUGH, // 100
		ERR_CAN_NOT_TRANSMIT_REFINE,
		ERR_LOW_LEVEL_TRANSMIT_REFINE,
		ERR_DEST_CAN_NOT_TRANSMIT_REFINE,
		ERR_TRANSMIT_REFINE_NEED_BIND,
		ERR_TRANSMIT_REFINE_NO_MATERIAL, // 105
		ERR_MAKE_SLOT_FAILURE,
		ERR_MAKE_SLOT_SUCCESS,
		ERR_FORBIDDED_OPERATION_IN_SAFE_LOCK,
		ERR_ELF_ADD_ATTRIBUTE_FAILED, // lgc
		ERR_ELF_ADD_GENIUS_FAILED,	  // 110
		ERR_ELF_PLAYER_INSERT_EXP_FAILED,
		ERR_ELF_INSERT_EXP_USE_PILL_FAILED,
		ERR_ELF_EQUIP_ITEM_FAILED,
		ERR_ELF_CHANGE_SECURE_STATUS_FAILED,
		ERR_ELF_DEC_ATTRIBUTE_FAILED, // 115
		ERR_ELF_FLUSH_GENIUS_FAILED,
		ERR_ELF_LEARN_SKILL_FAILED,
		ERR_ELF_FORGET_SKILL_FAILED,
		ERR_ELF_REFINE_CANNOT_REFINE,
		ERR_ELF_REFINE_TRANSMIT_FAILED, // 120
		ERR_ELF_REF_TRANS_FROM_TRADE_TO_UNTRADE,
		ERR_ELF_DECOMPOSE_FAILED,
		ERR_DECOMPOSE_UNTRADABLE_ELF,
		ERR_ELF_DECOMPOSE_EXP_ZERO,
		ERR_ELF_DESTROY_ITEM_FAILED, // 125
		ERR_ELF_NOT_ENOUGH_VIGOR,
		ERR_ELF_NOT_ENOUGH_STAMINA,
		ERR_CAST_ELF_SKILL_FAILED,
		ERR_ELF_REFINE_ACTIVATE_FAILED,
		ERR_ELF_CANNOT_UNEQUIP_IN_COMBAT_STATE, // 130
		ERR_CAST_ELF_SKILL_IN_COOLDOWN,
		ERR_ELF_CMD_IN_COOLDOWN,
		ERR_TRADER_MONEY_REACH_UPPER_LIMIT,
		ERR_TRADER_MONEY_ISNOT_ENOUGH,
		ERR_BUYER_MONEY_REACH_UPPER_LIMIT, // 135
		ERR_CANNOT_EQUIP_TRASHBOX_FASHION_ITEM,
		ERR_SECURITY_PASSWD_UNCHECKED,
		ERR_OPERTION_DENYED_IN_INVISIBLE,
		ERR_CANNOT_TOGGLE_MULTI_EXP,
		ERR_WEDDING_NOT_ONGOING, // 140
		ERR_WEDDING_CANNOT_BOOK,
		ERR_WEDDING_CANNOT_CANCELBOOK,
		ERR_ITEM_CANNOT_SHARPEN,
		ERR_FACTION_FORTRESS_OP_DENYED,
		ERR_FACTION_FORTRESS_OP_FAILED, // 145
		ERR_NOT_ENOUGH_FACTION_CONTRIB,
		ERR_FACTION_FORTRESS_ISKICK,
		ERR_SPOUSE_NOT_IN_SAME_SCENE,
		ERR_PET_CAN_NOT_BE_DYED,
		ERR_OPERTION_DENYED_IN_CUR_SCENE, // 150
		ERR_NOT_ENOUGH_FORCE_CONTRIB,
		ERR_NOT_ENOUGH_FORCE_REPU,
		ERR_NOT_ENOUGH_AWARD_TIME,
		ERR_ALREADY_JOIN_COUNTRY,
		ERR_NOT_JOIN_COUNTRY, // 155
		ERR_NOT_TEAM_LEADER,
		ERR_EQUIP_SIGNATURE_WRONG,
		ERR_ITEM_PRICE_MISMATCH,
		ERR_PET_CANNOT_EVOLUTION,
		ERR_PET_TYPE_WRONG, // 160
		ERR_ITEM_FORBID_SHOP,
		ERR_ITEM_FORBID_SELL,
		ERR_CANNOT_CHANGEDS,
		ERR_SEC_LEVEL_NOT_MATCH,
		ERR_PLAYER_RENAME,			 // 165
		ERR_NO_EQUAL_EQUIPMENT_FAIL, //  没有合适装备或装备孔
		ERR_NO_EQUAL_RECIPE_FAIL,	 // 和配方不匹配
		ERR_NO_EQUAL_SOURCE_FAIL,	 // 源魂石不匹配
		ERR_NO_EQUAL_DEST_FAIL,		 // 目标魂石不匹配
		ERR_MODIFY_ADDON_FAIL,		 // 魂石转化或替换执行出现错误 170
		ERR_SHOP_NOT_OPEN,			 // 商城没有开放
		ERR_TRY_REFINE_MERIDIAN_FAIL,
		ERR_CANNOT_REBUILD,
		ERR_CANNOT_SWITCH_IN_PARALLEL_WORLD,
		ERR_PARALLEL_WORLD_NOT_EXIST, // 175
		ERR_TOO_MANY_PLAYER_IN_PARALLEL_WORLD,
		ERR_REINCARNATION_CONDITION,
		ERR_REINCARNATION_REWRITE_TOME,
		ERR_REINCARNATION_ACTIVE_TOME,
		ERR_MINE_NOT_OWNER, // 180
		ERR_GENERALCARD_REBIRTH_CONDITION,
		ERR_GENERALCARD_INSERT_EXP,
		ERR_CANNOT_EQUIP_TRASHBOX_ITEM,
		ERR_MINE_SOUL_GATHER_TOO_MUCH, // 达到每日采集元魂上限
		ERR_USE_SOUL_TOP_LEVEL,		   // 命轮到满级了，不能使用元魂 185
		ERR_USE_SOUL_EXP_FULL,		   // 命轮经验满了，不能使用元魂
		ERR_MINE_GATHER_IS_COOLING,	   // 采矿正在冷却中
		ERR_ITEM_CANNOT_IMPROVE,	   // 无法进行改良
		ERR_MAFIA_NO_PERMISSION,	   // 帮派操作无权
		ERR_OTHER_ACTION_IN_EXECUTE,   // 190
		ERR_ACTION_DENYED_IN_NON_MOVE_SESSION,
		ERR_ATTACK_SESSION_DENYED_IN_ACTION,
		ERR_INSTANCE_REENTER_FAIL,
		ERR_CHANGE_GENDER_STATE,	  // 194 状态不是normal
		ERR_CHANGE_GENDER_GENDER,	  // 195 变成相同性别
		ERR_CHANGE_GENDER_CLS,		  // 196 职业限制
		ERR_CHANGE_GENDER_MARRIED,	  // 197 角色已婚
		ERR_CHANGE_GENDER_FASHION,	  // 198 佩戴时装或者时装武器
		ERR_CHANGE_GENDER_PROFILE,	  // 199 登记过情缘系统
		ERR_CHANGE_GENDER_TASK,		  // 200 存在性别相关任务
		ERR_MAKE_SLOT_FOR_DECOR_PROB, // 201 概率打孔失败 即未命中
		ERR_ASTROLABE_OPT_FAIL,
		ERR_ASTROLABE_SWALLOW_LIMIT,						   // 203
		ERR_SOLO_CHALLENGE_TOP_STAGE,						   // 204 已到达单人副本最高等级
		ERR_SOLO_CHALLENGE_FAILURE,							   // 205 进入单人副本失败
		ERR_SOLO_CHALLENGE_AWARD_FAILURE,					   // 206 单人副本发奖失败
		ERR_SOLO_CHALLENGE_SCORE_COST,						   // 207
		ERR_SOLO_CHALLENGE_SCORE_TOO_FEW,					   // 208 积分不足
		ERR_SOLO_CHALLENGE_SCORE_COST_COOLDOWN,				   // 209 COOLDOWN
		ERR_SOLO_CHALLENGE_SELECT_STAGE_COOLDOWN,			   // 210
		ERR_MNFACTION_NOT_IN_BATTLE,						   // 211 不在跨服帮战战场中
		ERR_NOT_IN_FACTION,									   // 212  没有跨服帮派信息
		ERR_MNFACTION_TRANSMIT_POS_FACTION,					   // 213 当前传送点不归属于本帮
		ERR_MNFACTION_SIGN_UP_C_NOT_ENOUGH_CITY,			   // 214 报名C级城不够5块
		ERR_MNFACTION_SIGN_UP_LOWER_TYPE,					   // 215 报名A级或B级，无低级领地
		ERR_MNFACTION_GATHER_FAILED,						   // 216 采矿失败
		ERR_MNFACTION_FACTION_GATHERING,					   // 217 本帮成员正在采集
		ERR_MNFACTION_BELONG_TO_OWN_FACTION,				   // 218 资源已归属本帮
		ERR_MNFACTION_HAVE_DESTROYED,						   // 219 资源塔已损毁
		ERR_REALM_LEVEL_NOT_MATCH,							   // 220 境界等级不够
		ERR_CARNIVAL_COUNT_LIMIT,							   // 221 跨服活动人数限制
		ERR_SOLO_CHALLENGE_FILTER_STACK_MAX,				   // 222 单人副本状态包叠加达到上限
		ERR_MNFACTION_MULTI_DOMAIN,							   // 223 一个角色只能进入一个战场
		ERR_MNFACTION_INVITE_COUNT_PERDOMAIN_MAXMUM,		   // 224 进入战场的角色太多
		ERR_MNFACTION_FORBID_ENTER,							   // 225 本帮派不允许进入
		ERR_CASH_VIP_LIMIT,									   // 226 消费vip等级不够
		ERR_FIX_POSITION_TRANSMIT_CANNOT_ADD_IN_THIS_WORLDTAG, // 227 此地图不支持定位传送
		ERR_FIX_POSITION_TRANSMIT_CANNOT_FIND,				   // 228 无此传送点
		ERR_FIX_POSITION_TRANSMIT_ENERGY_NOT_ENOUGH,		   // 229 定点传送能量不足
		ERR_SHOPPING_TIMES_LIMIT,							   // 230 达到限购次数
		ERR_FIX_POSITION_TRANSMIT_MAX_NUM,					   // 231 定位传送点达到最高数量
		ERR_FIX_POSITION_TRANSMIT_ENERGY_MAX,				   // 232 定位传送能量达到最大值
		ERR_SHOPPING_TIMES_LIMIT_ITEM_CANNOT_GIVE,			   // 233 限购商品不允许赠送
		ERR_SHOPPING_TIMES_LIMIT_ITEM_CANNOT_ASK_FOR,		   // 234 限购商品不允许索取
		ERR_SHOPPING_VIP_LIMIT_ITEM_CANNOT_GIVE,			   // 235 VIP等级限制物品不允许赠送
		ERR_SHOPPING_VIP_LIMIT_ITEM_CANNOT_ASK_FOR,			   // 236 VIP等级限制物品不允许索取

		// Fulano

		ERR_ARENA_LOSE_POINTS = 368,
		ERR_NEW_ERROR_RUNE = 374,
		ERR_RUNE_INCORRET_OPERATION,
		ERR_RUNE_MODEL_INCORRET,
		ERR_RUNE_SKILL_NO_INJECT,
		ERR_RUNE_SKILL_TIME_COOLDOWN,
		ERR_RUNE_TRASH_INCORRET,
		ERR_RUNE_TRASH_CAMAD_INCORRET,
		ERR_RUNE_TRASH_INCORRET_NUMBER,
		ERR_RUNE_TRASH_COMPLETE,
		ERR_RUNE_SLOT_OPEN,
		ERR_RUNE_SLOT_OCUPIED,
		ERR_RUNE_SLOT_NOT_OPEN,
		ERR_LAST_LEVEL_INCORRET,
		ERR_RUNE_CULTIVE_LOSE,
		ERR_RUNE_FAIL_SLOT_OPEN,
		ERR_RUNE_INJECT_SUCCESS,
		ERR_RUNE_LOCAL_INCORRET_INJECT,
		ERR_RUNE_EXISTENT,
		ERR_RUNE_INJECT_EXISTENT,
		ERR_RUNE_INJECT_FAIL,
		ERR_RUNE_DESINJECT_SUCCESS,
		ERR_RUNE_LOCAL_INCORRET_DESINJECT,
		ERR_RUNE_DESINJECT_NOT_EXISTENT,
		ERR_RUNE_DESINJECT_NOT_EXISTENT2,
		ERR_RUNE_DESINJECT_SUCCESS2,
		ERR_RUNE_EVOLUTION_SUCCESS,
		ERR_RUNE_EVOLUTION_MATERIAL_INSUFFICIENT,
		ERR_RUNE_EVOLUTION_LOSE_LEVEL_MATERIAL,
		ERR_RUNE_EVOLUTION_MATERIAL_INCORRET,
		ERR_RUNE_EVOLUTION_NOT_SUCCESS,

		ERR_LOW_LEVEL = 558,

		ERR_CODEX_BEGIN = 571,								  //"Início"
		ERR_CODEX_ITEM_COLLECTIBLES,						  //"Sem itens coletáveis"
		ERR_CODEX_COLLECTION_FAILED,						  //"A coleta falhou"
		ERR_CODEX_MAX_COLLECTIBLES_CAPACITY_REACHED,		  //"Capacidade máxima de colecionáveis atingida"
		ERR_CODEX_CANNOT_REPLACE_FASHION,					  //"Não é possível substituir Moda"
		ERR_CODEX_CANNOT_DYE_FASHION,						  //"Não é possível tingir Moda"
		ERR_CODEX_FASHION_ALREADY_EQUIPPED,					  //"Esta Moda já foi equipada"
		ERR_CODEX_NOT_FOUND_IN_CODEX,						  //"Não existente no Códice"
		ERR_CODEX_CANNOT_EQUIP_FASHION,						  //"Não é possível equipar Moda"
		ERR_CODEX_INCORRECT_EQUIPMENT_SLOT,					  //"Espaço de equipamento incorreto"
		ERR_CODEX_CANNOT_RECEIVE_REWARD,					  //"Não é possível receber recompensa"
		ERR_CODEX_BACKPACK_FULL,							  //"A mochila está cheia"
		ERR_CODEX_INCORRECT_GENDER,							  //"Gênero Incorreto"
		ERR_CODEX_CANNOT_COLLECT_FLYING_MOUNT,				  //"Não é possível coletar a Montaria Alada"
		ERR_CODEX_FLYING_MOUNT_COLLECTIBLES_CAPACITY_REACHED, //"Sem itens coletáveis"
		ERR_CODEX_CANNOT_EQUIP_FLYING_MOUNT,				  //"Capacidade máxima de colecionáveis atingida"
		ERR_CODEX_FLYING_MOUNT_NOT_FOUND,					  //"Não é possível equipar a Montaria Alada"
		ERR_CODEX_BACKPACK_FULL_FLYING_MOUNT,				  //"A Montaria Alada não existe"
		ERR_CODEX_CANNOT_UPGRADE_FLYING_MOUNT,				  //"A mochila está cheia"
		ERR_CODEX_FLYING_MOUNT_ALREADY_EQUIPPED,			  //"Não é possível aprimorar a Montaria Alada"
		ERR_CODEX_CANNOT_CHANGE_FLYING_MOUNT_SHORTCUT,		  //"Esta Montaria Alada foi equipada"
		ERR_CODEX_CANNOT_SWAP_FLYING_MOUNT_SHORTCUT,		  //"Não é possível alterar atalho da Montaria Alada"
		ERR_CODEX_INCORRECT_EQUIPMENT_TYPE,					  //"Não é possível trocar atalho de Montaria Alada"
		ERR_CODEX_CANNOT_COLLECT_PET,						  //"Tipo de equipamento incorreto"
		ERR_CODEX_PET_COLLECTIBLES_CAPACITY_REACHED,		  //"Não é possível coletar Mascote"
		ERR_CODEX_FAILED_TO_ACTIVATE_PET,					  //"Sem itens coletáveis"
		ERR_CODEX_INCORRECT_CODEX_REWARD_TYPE,				  //"Capacidade máxima de colecionáveis atingida"
		ERR_CODEX_INCOMPATIBLE_RACES,						  //"Falha ao ativar o Mascote"
		ERR_CODEX_PET_NOT_FOUND,							  //"Tipo incorreto de recompensa do Códice"
		ERR_CODEX_PET_SUMMONED_CANNOT_BE_DISMISSED,			  //"Raças não correspondentes"
		ERR_CODEX_PET_ACTIVATED_CANNOT_BE_COLLECTED,		  //"O Mascote não existe"
		ERR_CODEX_CANNOT_RECEIVE_CODEX_REWARD,				  //"O Mascote foi conjurado. Não é possível retirar"
		ERR_CODEX_CANNOT_RENAME_PET,						  //"O Mascote está ativado, altere o status para não ativado para poder coletá-lo"
		ERR_CODEX_INSUFFICIENT_CODEX_COLLECTION_PROGRESS,	  //"Não é possível receber recompensa de Códice"
		ERR_CODEX_TIME_LIMITED_ITEM,						  //"Não é possível renomear Mascote"
		ERR_CODEX_LEVEL_DOES_NOT_MATCH,						  //"Progresso de Coleta do Códice é insuficiente para a recompensa"
		ERR_CODEX_LIMIT_DOES_NOT_MATCH,						  //"Item de tempo limitado"
		ERR_CODEX_CULTIVATION_DOES_NOT_MATCH,				  //"O nível não corresponde"
		ERR_CODEX_PET_BACKPACK_FULL,						  //"O limite não corresponde"
		ERR_CODEX_ALREADY_ADOPTED,							  //"O Cultivo não corresponde"
		ERR_CODEX_CANNOT_ADOPT,								  //"A mochila de Mascote está cheia"

	};
};

#pragma pack()
#endif

