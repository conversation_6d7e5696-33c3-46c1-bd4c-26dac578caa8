#ifndef __C2S_HEADER_H__
#define __C2S_HEADER_H__

#include "../types.h"

#pragma pack(1)

namespace C2S
{
	enum MOVE_MODE
	{
		MOVE_MODE_WALK		= 0x00,
		MOVE_MODE_RUN		= 0x01,
		MOVE_MODE_STAND		= 0x02,
		MOVE_MODE_FALL		= 0x03,
		MOVE_MODE_SLIDE		= 0x04,
		MOVE_MODE_KNOCK		= 0x05,
		MOVE_MODE_FLY_FALL 	= 0x06,
		MOVE_MODE_RETURN	= 0x07,
		MOVE_MODE_JUMP		= 0x08,
		MOVE_MODE_PULL		= 0x09,
		MOVE_MODE_BLINK		= 0x0A,
		MOVE_MASK_DEAD		= 0x20,
		MOVE_MASK_SKY		= 0x40,
		MOVE_MASK_WATER		= 0x80

	};

	enum FORCE_ATTACK_MASK
	{	
		FORCE_ATTACK		= 0x01,		//强制攻击
		FORCE_ATTACK_NO_MAFIA	= 0x02,		//不攻击帮派成员
		FORCE_ATTACK_NO_WHITE	= 0x04,		//不攻击白名
		FORCE_ATTACK_NO_MAFIA_ALLIANCE	= 0x08,		//不攻击帮派同盟
		FORCE_ATTACK_NO_SAME_FORCE		= 0x10,		//不攻击同势力玩家
		FORCE_ATTACK_MASK_ALL	= 0x1F,
	};

	enum REFUSE_BLESS_MASK
	{
		REFUSE_NEUTRAL_BLESS = 0x01,		//不接受中性祝福
		REFUSE_NON_TEAMMATE_BLESS = 0x02,	//不接受非队友祝福
		REFUSE_BLESS_MASK_ALL = 0x03,
	};

	enum ASTROLABE_OPT_TYPE
	{
		ASTROLABE_OPT_SWALLOW,
		ASTROLABE_OPT_ADDON_ROLL,
		ASTROLABE_OPT_APTIT_INC,
		ASTROLABE_OPT_SLOT_ROLL,
		ASTROLABE_OPT_LOCK_UNLOCK, /*158+*/
		ASTROLABE_OPT_SAVE_ADDONS, /*176+*/
	};

	enum SOLO_CHALLENGE_OPT_TYPE
	{
		SOLO_CHALLENGE_OPT_SELECT_STAGE,
		SOLO_CHALLENGE_OPT_SELECT_AWARD,
		SOLO_CHALLENGE_OPT_SCORE_COST,
		SOLO_CHALLENGE_OPT_CLEAR_FILTER,
		SOLO_CHALLENGE_OPT_DELIVERSCORE,
		SOLO_CHALLENGE_OPT_LEAVE_THE_ROOM,
	};

	enum FIX_POSITION_TRANSMIT_OPT_TYPE
	{
		FIX_POSITION_TRANSMIT_OPT_ADD_POSITION,
		FIX_POSITION_TRANSMIT_OPT_DELETE_POSITION,
		FIX_POSITION_TRANSMIT_OPT_TRANSMIT,
		FIX_POSITION_TRANSMIT_OPT_RENAME,
	};

	enum
	{
		PLAYER_MOVE,
		LOGOUT,
		SELECT_TARGET,
		NORMAL_ATTACK,
		RESURRECT_IN_TOWN,	//城镇复生
//5		
		RESURRECT_BY_ITEM,	//物品复生
		PICKUP,			//检起物品或者金钱
		STOP_MOVE,
		UNSELECT,		//中止当前选定的目标
		GET_ITEM_INFO,		//取得物品特定位置的信息

//10
		GET_INVENTORY,		//取得某个位置上的所有物品列表
		GET_INVENTORY_DETAIL,	//取得某个位置上的所有物品列表，包含详细的物品数据
		EXCHANGE_INVENTORY_ITEM,
		MOVE_INVENTORY_ITEM,
		DROP_INVENTORY_ITEM,

//15		
		DROP_EQUIPMENT_ITEM,
		EXCHANGE_EQUIPMENT_ITEM,
		EQUIP_ITEM,		//装备物品，将物品栏上和装备栏上的两个位置进行调换
		MOVE_ITEM_TO_EQUIPMENT,
		GOTO,

//20		
		DROP_MONEY,		//扔出钱到地上
		SELF_GET_PROPERTY,
		SET_STATUS_POINT,	//设置属性点数
		GET_EXTPROP_BASE,
		GET_EXTPROP_MOVE,

//25
		GET_EXTPROP_ATTACK,
		GET_EXTPROP_DEFENSE,
		TEAM_INVITE,
		TEAM_AGREE_INVITE,
		TEAM_REJECT_INVITE,
//30
		TEAM_LEAVE_PARTY,
		TEAM_KICK_MEMBER,
		TEAM_GET_TEAMMATE_POS,
		GET_OTHERS_EQUIPMENT,
		CHANGE_PICKUP_FLAG,

//35		
		SERVICE_HELLO,
		SERVICE_GET_CONTENT,
		SERVICE_SERVE,
		GET_OWN_WEALTH,
		GET_ALL_DATA,
//40		
		USE_ITEM,
		CAST_SKILL,
		CANCEL_ACTION,
		RECHARGE_EQUIPPED_FLYSWORD,
		RECHARGE_FLYSWORD,
//45		
		USE_ITEM_WITH_TARGET,
		SIT_DOWN,
		STAND_UP,
		EMOTE_ACTION,
		TASK_NOTIFY,
//50
		ASSIST_SELECT,
		CONTINUE_ACTION,
		STOP_FALL,	//终止跌落
		GET_ITEM_INFO_LIST,
		GATHER_MATERIAL,
//55		
		GET_TRASHBOX_INFO,
		EXCHANGE_TRASHBOX_ITEM,
		MOVE_TRASHBOX_ITEM,
		EXHCANGE_TRASHBOX_INVENTORY,
		MOVE_TRASHBOX_ITEM_TO_INVENTORY,
//60		
		MOVE_INVENTORY_ITEM_TO_TRASHBOX,
		EXCHANGE_TRASHBOX_MONEY,
		TRICKS_ACTION,
		SET_ADV_DATA,
		CLR_ADV_DATA,
//65		
		TEAM_LFG_REQUEST,
		TEAM_LFG_REPLY,
		QUERY_PLAYER_INFO_1,
		QUERY_NPC_INFO_1,
		SESSION_EMOTE_ACTION,
//70 	
		CONCURRECT_EMOTE_REQUEST,
		CONCURRECT_EMOTE_REPLY,
		TEAM_CHANGE_LEADER,
		DEAD_MOVE,
		DEAD_STOP_MOVE,

//75
		ENTER_SANCTUARY,
		OPEN_PERSONAL_MARKET,
		CANCEL_PERSONAL_MARKET,
		QUERY_PERSONAL_MARKET_NAME,
		COMPLETE_TRAVEL,

//80
		CAST_INSTANT_SKILL,
		DESTROY_ITEM,
		ENABLE_PVP_STATE,
		DISABLE_PVP_STATE,
		TEST_PERSONAL_MARKET,

//85
		SWITCH_FASHION_MODE,
		REGION_TRANSPORT,
		RESURRECT_AT_ONCE,
		NOTIFY_POS_TO_MEMBER,
		CAST_POS_SKILL,

//90	
		ACTIVE_RUSH_MODE,
		QUERY_DOUBLE_EXP_INFO,
		DUEL_REQUEST,
		DUEL_REPLY,
		BIND_PLAYER_REQUEST,

//95
		BIND_PLAYER_INVITE,
		BIND_PLAYER_REQUEST_REPLY,
		BIND_PLAYER_INVITE_REPLY,
		BIND_PLAYER_CANCEL,
		QUERY_OTHER_EQUIP_DETAIL,

//100
		SUMMON_PET,
		RECALL_PET,
		BANISH_PET,
		PET_CTRL_CMD,
		DEBUG_DELIVERY_CMD,
	
//105
		DEBUG_GS_CMD,
		MALL_SHOPPING,
		GET_WALLOW_INFO,
		TEAM_DISMISS_PARTY,
		USE_ITEM_WITH_ARG,

//110		
		QUERY_CASH_INFO,
		ELF_ADD_ATTRIBUTE,//lgc
		ELF_ADD_GENIUS,
		ELF_PLAYER_INSERT_EXP,
		ELF_EQUIP_ITEM,

//115
		ELF_CHANGE_SECURE_STATUS,
		CAST_ELF_SKILL,
		RECHARGE_EQUIPPED_ELF,
		GET_MALL_ITEM_PRICE,
		EQUIP_TRASHBOX_FASHION_ITEM,	//装备时装，将时装仓库中的时装与身上的交换
		
//120
		CHECK_SECURITY_PASSWD,		//验证安全密码(同仓库密码)，当使用受保护的功能时需要输入
		NOTIFY_FORCE_ATTACK,
		DIVIDEND_MALL_SHOPPING,
		GET_DIVIDEND_MALL_ITEM_PRICE,
		CHOOSE_MULTI_EXP,
	
//125
		TOGGLE_MULTI_EXP,
		MULTI_EXCHANGE_ITEM,
		SYSAUCTION_OP,
		CALC_NETWORK_DELAY,
		GET_FACTION_FORTRESS_INFO,

//130
		CONGREGATE_REPLY,
		GET_FORCE_GLOBAL_DATA,
		PRODUCE4_CHOOSE,	// 新继承生产，生产结束之后玩家选择新物品还是旧物品
		RECHARGE_ONLINE_AWARD,
		TOGGLE_ONLINE_AWARD,
		
//135
		QUERY_PROFIT_TIME,	//客户端请求收益时间数据
		ENTER_PK_PROTECTED,	// 进入新手保护区
		COUNTRYBATTLE_GET_PERSONAL_SCORE,
		GET_SERVER_TIMESTAMP,
		COUNTRYBATTLE_LEAVE,

//140	
		GET_CASH_MONEY_EXCHG_RATE,
		EVOLUTION_PET,
		ADD_PET_EXP,
		REBUILD_PET_NATURE,
		REBUILD_PET_INHERIT_RATIO,

//145
		PET_REBUILDINHERIT_CHOOSE,
		PET_REBUILDNATURE_CHOOSE,
		EXCHANGE_WANMEI_YINPIAO,
		PLAYER_GIVE_PRESENT,
		PLAYER_ASK_FOR_PRESENT,

//150
		TRY_REFINE_MERIDIAN,
		COUNTRYBATTLE_GET_STRONGHOLD_STATE,
		QUERY_TOUCH_POINT,
		COST_TOUCH_POINT,
		QUERY_TITLE,

//155
		CHANGE_CURR_TITLE,
		DAILY_SIGNIN,
		LATE_SIGNIN,
		APPLY_SIGNIN_AWARD,
		REFRESH_SIGNIN,

//160
		SWITCH_IN_PARALLEL_WORLD,
		QUERY_PARALLEL_WORLD,
		GET_REINCARNATION_TOME,
		REWRITE_REINCARNATION_TOME,
		ACTIVATE_REINCARNATION_TOME,

//165
		QUERY_UNIQUE_DATA,
		AUTO_TEAM_SET_GOAL,
		AUTO_TEAM_JUMP_TO_GOAL,
		TRICKBATTLE_LEAVE,
		TRICKBATTLE_UPGRADE_CHARIOT,

//170
		SWALLOW_GENERALCARD,
		EQUIP_TRASHBOX_ITEM,
		QUERY_TRICKBATTLE_CHARIOTS,
		COUNTRYBATTLE_LIVE_SHOW,
		SEND_MASS_MAIL,
//175
		RANDOM_MALL_SHOPPING,
		QUERY_MAFIA_PVP_INFO,
        QUERY_CAN_INHERIT_ADDONS,
        ACTIVATE_REGION_WAYPOINTS,
	 	INSTANCE_REENTER_REQUEST,
//180
		ASTROLABE_OPERATE_REQUEST,
        SOLO_CHALLENGE_OPERATE_REQUEST,
        PROPERTY_SCORE_REQUEST,
		MNFACTION_GET_DOMAIN_DATA,
		PICKUP_ALL,
//185
		FIX_POSITION_TRANSMIT_OPERATE_REQUEST,
		REMOTE_REPAIR,
		GET_CASH_VIP_MALL_ITEM_PRICE,
		CASH_VIP_MALL_SHOPPING,
        UPDATE_ENEMYLIST,
//190
        LOOKUP_ENEMY,
        RESURRECT_BY_CASH,
		HOME_OPERATE_REQUEST,
		HOME_LEAVE,
		HOME_REFRESH_TASK,
//195
		HOME_KICK_OUT_PLAYERS,
		HOME_ENTER_REQUEST,
// 155-162
		LOTTERY_MANAGER,
		CGAME_SEND_198,
		LIB_ITEMS_MANAGER,
//200
		CGAME_SEND_200,
		ARENA_MANAGER,
		GLYPH_MANAGER,
		ARENA_BATTLE_ENTER,
		ARENA_BATTLE_LEAVE,
//205
		ENTER_CARRIER,
		LEAVE_CARRIER,
		CGAME_SEND_207,
		CGAME_SEND_208,
		CGAME_SEND_209,
//210
		CGAME_SEND_210,
		CGAME_SEND_211,
		CGAME_SEND_212,
		CGAME_SEND_213,
		CGAME_SEND_214,
//215
		CGAME_SEND_215,
		CGAME_SEND_216,
		CGAME_SEND_217,
		CARD_MANAGER,
		CGAME_SEND_219,
//220
		CGAME_SEND_220,
		ENGRAVE_CHOOSE,
		CGAME_SEND_222,
		CARD_S_DECOMPOSE,
		REPOSITORY_CHANGE_INC,
//225
		ARMOR_MANAGER_SPIRIT, // Espírito G17
		ARMOR_MANAGER_CRYSTAL, // Cristal G17
		CAST_SHIELD_SKILL,
		CGAME_SEND_228,
		CGAME_SEND_229,
// 162-172 		
		CGAME_SEND_230,
		CGAME_SEND_231,
		CGAME_SEND_232,
		SAFE_LOCK_NEW_INC,
		CHANGE_GOLD_SILVER,
		
		CGAME_SEND_235,
		LOTTERY2_MANAGER,
		CGAME_SEND_237,
		CELESTIAL_MEMORIAL_MANAGER,
		CGAME_SEND_239,
		
		CGAME_SEND_240,
		CAMP_TOKEN_MANAGER,
		CGAME_SEND_242,
		SKILL_PET_MOVE,
		RES_PET_CMD,
		
		PET_SKIN_INC,
		CODEX_CONSUM_FASHION_SINGLE,
		CODEX_REWARD_FIRST,
		CODEX_CHANGE_SINGLE_FASHION,
		CODEX_FASHION_EQUIP, // 249
		
		CODEX_REQUEST_STORAGE,
		CODEX_CONSUM_FLY_SINGLE,
		CODEX_CHANGE_FLY_EQUIP,
		CGAME_SEND_253,
		CGAME_SEND_254,
		
		CODEX_CONSUM_MOUNT_SINGLE,
		CODEX_GAIN_PET,
		CODEX_FREE_PET,
		CODEX_REWARD_TITLE,
		CODEX_CONSUM_FASHION_MUTIPLE,
		
		CGAME_SEND_260,
		CGAME_SEND_261,
		CGAME_SEND_262,
		CGAME_SEND_263,
		CGAME_SEND_264,
		
		CGAME_SEND_265,
		CGAME_SEND_266,
		CGAME_SEND_267,
		CGAME_SEND_268,
		ACTIVITY_EVENT_REWARD_LOGIN,
		
		ACTIVITY_EVENT_MANAGER,
		ENABLE_FASHION_WEAPON,
		WORLD_POINTS_REWARD,
		WORLD_POINTS_MANAGER,
		CGAME_SEND_274,
// 172+ 
		OPEN_FASHION_GSHOP_BOX = 281, // size = 10
		PORTATIL_PICTURE = 282,
		CGAME_SEND_296 = 296,
	};
	
	enum //  gm
	{	
		GM_COMMAND_START = 10000,
		GMCMD_MOVE_TO_PLAYER,	
		GMCMD_RECALL_PLAYER,	
		GMCMD_OFFLINE,			
		GMCMD_TOGGLE_INVISIBLE,	
		GMCMD_TOGGLE_INVINCIBLE,
		GMCMD_DROP_GENERATOR,	
		GMCMD_ACTIVE_SPAWNER,	
		GMCMD_GENERATE_MOB,        

		GMCMD_PLAYER_INC_EXP,	
		GMCMD_RESURRECT,		
		GMCMD_ENDUE_ITEM,			
		GMCMD_ENDUE_SELL_ITEM,	
		GMCMD_REMOVE_ITEM,		
		GMCMD_ENDUE_MONEY,		
		GMCMD_ENABLE_DEBUG_CMD,	
		GMCMD_RESET_PROP,
		GMCMD_GET_COMMON_VALUE,	
		GMCMD_QUERY_SPEC_ITEM,
		GMCMD_REMOVE_SPEC_ITEM,
		GMCMD_OPEN_ACTIVITY,	
		GMCMD_CHANGE_DS,
		GMCMD_QUERY_UNIQUE_DATA,
		GM_COMMAND_END,
	};
	
	enum //  main item
	{	
		GET_MAIN_INFO = 743,
		SET_COLOR_NAME = 744,
	};
	
	
};


#pragma pack()
#endif


